# Use an official Python runtime as a parent image
FROM python:3.9-slim-buster

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file and install dependencies
COPY flet_hvac_interface/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire flet_hvac_interface directory into the container
COPY flet_hvac_interface/ ./flet_hvac_interface/

# Expose the port Flet runs on
EXPOSE 8550

# Command to run the Flet application
CMD ["flet", "run", "--host", "0.0.0.0", "--port", "8550", "flet_hvac_interface/main.py", "--web"]
