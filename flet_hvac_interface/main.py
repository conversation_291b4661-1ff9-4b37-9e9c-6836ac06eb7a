"""
🔧 HVAC CRM Flet Interface - Main Entry Point
Cutting-edge future-proof interface for backend interaction
"""

import flet as ft
import asyncio
import logging
import threading
import time
from datetime import datetime

# Import interface components
from .interfaces.dashboard import DashboardInterface
from .interfaces.customer_profiles import CustomerProfilesInterface
from .interfaces.email_intelligence import EmailIntelligenceInterface
from .interfaces.service_orders import ServiceOrdersInterface
from .interfaces.equipment_registry import EquipmentRegistryInterface
from .interfaces.financial_dashboard import FinancialDashboardInterface

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HVACCRMInterface:
    """Main HVAC CRM Flet Interface Application"""
    
    def __init__(self):
        self.page = None
        self.current_view = "dashboard"
        self.data_sources_status = {
            "email_monitor": {"status": "running", "last_update": datetime.now()},
            "transcription": {"status": "idle", "last_update": datetime.now()},
            "ai_analysis": {"status": "processing", "last_update": datetime.now()},
            "database_sync": {"status": "syncing", "last_update": datetime.now()}
        }
        self.threads = {}
        self.running = True

        # Initialize interface components (will be created when page is available)
        self.dashboard_interface = None
        self.customer_profiles_interface = None
        self.email_intelligence_interface = None
        self.service_orders_interface = None
        self.equipment_registry_interface = None
        self.financial_dashboard_interface = None

        logger.info("🚀 HVAC CRM Interface initialized")
    
    def main(self, page: ft.Page):
        """Main application entry point"""
        self.page = page

        # Configure page
        page.title = "🔧 HVAC CRM - Cosmic Interface"
        page.window_width = 1400
        page.window_height = 900
        page.theme_mode = ft.ThemeMode.DARK
        page.padding = 0

        # Set theme and create layout
        page.theme = ft.Theme(color_scheme_seed="#2196F3", use_material3=True)

        # Initialize interface components
        self._initialize_interfaces()

        self.create_main_layout()
        self.start_background_threads()

        logger.info("✅ HVAC CRM Interface started successfully")

    def _initialize_interfaces(self):
        """Initialize all interface components"""
        try:
            self.dashboard_interface = DashboardInterface(self.page)
            self.customer_profiles_interface = CustomerProfilesInterface(self.page)
            self.email_intelligence_interface = EmailIntelligenceInterface(self.page)
            self.service_orders_interface = ServiceOrdersInterface(self.page)
            self.equipment_registry_interface = EquipmentRegistryInterface(self.page)
            self.financial_dashboard_interface = FinancialDashboardInterface(self.page)
            logger.info("✅ All interface components initialized")
        except Exception as e:
            logger.error(f"❌ Error initializing interfaces: {e}")
            # Create fallback interfaces
            self.dashboard_interface = None
            self.customer_profiles_interface = None
            self.email_intelligence_interface = None
            self.service_orders_interface = None
            self.equipment_registry_interface = None
            self.financial_dashboard_interface = None
    def create_main_layout(self):
        """Create the main application layout with cosmic-level design"""
        # Navigation rail with cosmic styling
        nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            group_alignment=-0.9,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.DASHBOARD_OUTLINED,
                    selected_icon=ft.icons.DASHBOARD,
                    label="Dashboard"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.PEOPLE_OUTLINED,
                    selected_icon=ft.icons.PEOPLE,
                    label="Customers"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.EMAIL_OUTLINED,
                    selected_icon=ft.icons.EMAIL,
                    label="Email Intel"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ASSIGNMENT_OUTLINED,
                    selected_icon=ft.icons.ASSIGNMENT,
                    label="Service Orders"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SETTINGS_OUTLINED,
                    selected_icon=ft.icons.SETTINGS,
                    label="Equipment"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ATTACH_MONEY_OUTLINED,
                    selected_icon=ft.icons.ATTACH_MONEY,
                    label="Financial"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS_OUTLINED,
                    selected_icon=ft.icons.ANALYTICS,
                    label="Analytics"
                ),
            ],
            on_change=self.navigation_changed,
        )
        
        # Main content area
        self.content_area = ft.Container(
            content=self.create_dashboard_view(),
            expand=True,
            padding=20,
        )
        
        # Main layout
        main_layout = ft.Row(
            [
                nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ],
            expand=True,
        )
        
        self.page.add(main_layout)
        self.page.update()    
    def navigation_changed(self, e):
        """Handle navigation changes"""
        views = ["dashboard", "customers", "email_intel", "service_orders", "equipment", "financial", "analytics"]
        self.current_view = views[e.control.selected_index]

        # Update content based on selection
        try:
            if self.current_view == "dashboard":
                if self.dashboard_interface:
                    self.content_area.content = self.dashboard_interface.create_interface()
                else:
                    self.content_area.content = self.create_dashboard_view()
            elif self.current_view == "customers":
                if self.customer_profiles_interface:
                    self.content_area.content = self.customer_profiles_interface.create_interface()
                else:
                    self.content_area.content = self.create_customers_view()
            elif self.current_view == "email_intel":
                if self.email_intelligence_interface:
                    self.content_area.content = self.email_intelligence_interface.create_interface()
                else:
                    self.content_area.content = self.create_email_intelligence_view()
            elif self.current_view == "service_orders":
                if self.service_orders_interface:
                    self.content_area.content = self.service_orders_interface.create_interface()
                else:
                    self.content_area.content = self.create_service_orders_view()
            elif self.current_view == "equipment":
                if self.equipment_registry_interface:
                    self.content_area.content = self.equipment_registry_interface.create_interface()
                else:
                    self.content_area.content = self.create_equipment_view()
            elif self.current_view == "financial":
                if self.financial_dashboard_interface:
                    self.content_area.content = self.financial_dashboard_interface.create_interface()
                else:
                    self.content_area.content = self.create_financial_view()
            elif self.current_view == "analytics":
                self.content_area.content = self.create_analytics_view()
        except Exception as e:
            logger.error(f"❌ Error switching to view {self.current_view}: {e}")
            self.content_area.content = self.create_error_view(str(e))

        self.page.update()
        logger.info(f"🔄 Switched to view: {self.current_view}")
    
    def create_dashboard_view(self):
        """Create the main dashboard view"""
        # Status cards for data sources
        status_cards = ft.Row([
            self.create_status_card("📧 Email Monitor", "email_monitor", ft.colors.GREEN),
            self.create_status_card("🎤 Transcription", "transcription", ft.colors.ORANGE),
            self.create_status_card("🤖 AI Analysis", "ai_analysis", ft.colors.BLUE),
            self.create_status_card("💾 Database Sync", "database_sync", ft.colors.PURPLE),
        ], spacing=20)
        
        # Real-time metrics
        metrics_section = ft.Container(
            content=ft.Column([
                ft.Text("📊 Real-time Metrics", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([
                    self.create_metric_card("Emails Processed", "1,247", "+12%"),
                    self.create_metric_card("Transcriptions", "89", "+5%"),
                    self.create_metric_card("Customer Profiles", "456", "+8%"),
                    self.create_metric_card("AI Insights", "234", "+15%"),
                ], spacing=20),
            ]),
            padding=20,
            margin=ft.margin.only(top=20),
        )
        
        return ft.Column([
            ft.Text("🔧 HVAC CRM - Cosmic Dashboard", size=32, weight=ft.FontWeight.BOLD),
            status_cards,
            metrics_section,
        ], spacing=20)    
    def create_status_card(self, title, status_key, color):
        """Create a status card for data sources"""
        status = self.data_sources_status[status_key]
        status_color = ft.colors.GREEN if status["status"] == "running" else ft.colors.ORANGE
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text(title, size=16, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.Icon(ft.icons.CIRCLE, color=status_color, size=12),
                        ft.Text(status["status"].upper(), size=12),
                    ]),
                    ft.Text(f"Last: {status['last_update'].strftime('%H:%M:%S')}", size=10),
                ]),
                padding=15,
                width=200,
            ),
            elevation=3,
        )
    
    def create_metric_card(self, title, value, change):
        """Create a metric card"""
        change_color = ft.colors.GREEN if change.startswith('+') else ft.colors.RED
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text(title, size=14),
                    ft.Text(value, size=24, weight=ft.FontWeight.BOLD),
                    ft.Text(change, size=12, color=change_color),
                ]),
                padding=15,
                width=180,
            ),
            elevation=2,
        )
    
    def create_customers_view(self):
        """Create customer profiles view"""
        return ft.Column([
            ft.Text("👥 Customer Profiles - 360° View", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Comprehensive customer data integration", size=16),
            ft.Container(
                content=ft.Text("🚧 Customer profiles interface coming soon...", size=18),
                padding=20,
                bgcolor=ft.colors.BLUE_GREY_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_email_intelligence_view(self):
        """Create email intelligence view"""
        return ft.Column([
            ft.Text("📧 Email Intelligence", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("AI-powered email analysis and processing", size=16),
            ft.Container(
                content=ft.Text("🤖 Email intelligence dashboard coming soon...", size=18),
                padding=20,
                bgcolor=ft.colors.GREEN_100,
                border_radius=10,
            ),
        ], spacing=20)    
    def create_documents_view(self):
        """Create document processing view"""
        return ft.Column([
            ft.Text("📄 Document Processing", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("OCR, invoice extraction, and document analysis", size=16),
            ft.Container(
                content=ft.Text("📋 Document processing interface coming soon...", size=18),
                padding=20,
                bgcolor=ft.colors.ORANGE_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_calendar_view(self):
        """Create calendar integration view"""
        return ft.Column([
            ft.Text("📅 Calendar Integration", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Intelligent scheduling and calendar optimization", size=16),
            ft.Container(
                content=ft.Text("🗓️ Calendar management interface coming soon...", size=18),
                padding=20,
                bgcolor=ft.colors.PURPLE_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_analytics_view(self):
        """Create analytics dashboard view"""
        return ft.Column([
            ft.Text("📊 Analytics Dashboard", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Real-time insights and business intelligence", size=16),
            ft.Container(
                content=ft.Text("📈 Analytics dashboard coming soon...", size=18),
                padding=20,
                bgcolor=ft.colors.CYAN_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_service_orders_view(self):
        """Create service orders fallback view"""
        return ft.Column([
            ft.Text("🔧 Service Orders", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Kanban workflow management", size=16),
            ft.Container(
                content=ft.Text("🚧 Service orders interface loading...", size=18),
                padding=20,
                bgcolor=ft.colors.BLUE_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_equipment_view(self):
        """Create equipment registry fallback view"""
        return ft.Column([
            ft.Text("🔧 Equipment Registry", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("HVAC equipment lifecycle management", size=16),
            ft.Container(
                content=ft.Text("🚧 Equipment registry interface loading...", size=18),
                padding=20,
                bgcolor=ft.colors.TEAL_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_financial_view(self):
        """Create financial dashboard fallback view"""
        return ft.Column([
            ft.Text("💰 Financial Dashboard", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Business intelligence & analytics", size=16),
            ft.Container(
                content=ft.Text("🚧 Financial dashboard interface loading...", size=18),
                padding=20,
                bgcolor=ft.colors.GREEN_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_error_view(self, error_message: str):
        """Create error view"""
        return ft.Column([
            ft.Text("❌ Interface Error", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.RED),
            ft.Text("An error occurred while loading the interface", size=16),
            ft.Container(
                content=ft.Column([
                    ft.Text("Error Details:", weight=ft.FontWeight.BOLD),
                    ft.Text(error_message, size=12, color=ft.colors.RED),
                    ft.ElevatedButton(
                        "Retry",
                        on_click=lambda e: self.page.update()
                    )
                ], spacing=10),
                padding=20,
                bgcolor=ft.colors.RED_100,
                border_radius=10,
            ),
        ], spacing=20)
    def start_background_threads(self):
        """Start background processing threads"""
        # Email monitoring thread
        email_thread = threading.Thread(
            target=self.email_monitoring_worker,
            name="EmailMonitor",
            daemon=True
        )
        email_thread.start()
        self.threads['email'] = email_thread
        
        # Real-time updates thread
        updates_thread = threading.Thread(
            target=self.real_time_updates_worker,
            name="RealTimeUpdates",
            daemon=True
        )
        updates_thread.start()
        self.threads['updates'] = updates_thread
        
        logger.info("🧵 Background threads started")
    
    def email_monitoring_worker(self):
        """Background worker for email monitoring"""
        while self.running:
            try:
                # Simulate email processing
                time.sleep(5)
                self.data_sources_status["email_monitor"]["last_update"] = datetime.now()
                logger.info("📧 Email monitoring tick")
            except Exception as e:
                logger.error(f"❌ Email monitoring error: {e}")
    
    def real_time_updates_worker(self):
        """Background worker for real-time UI updates"""
        while self.running:
            try:
                # Update UI every 2 seconds
                time.sleep(2)
                if self.page and self.current_view == "dashboard":
                    # Update timestamps in status cards
                    for status in self.data_sources_status.values():
                        if status["status"] == "running":
                            status["last_update"] = datetime.now()
                    
                    # Trigger UI update (in real app, use page.update() carefully)
                    logger.debug("🔄 Real-time update tick")
            except Exception as e:
                logger.error(f"❌ Real-time updates error: {e}")

# Application entry point
def main():
    """Launch the HVAC CRM Flet interface"""
    app = HVACCRMInterface()
    ft.app(target=app.main, view=ft.AppView.FLET_APP)

if __name__ == "__main__":
    main()