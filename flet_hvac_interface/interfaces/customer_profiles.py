"""
👥 Customer Profiles Interface - 360° Customer Intelligence
Unified customer view with AI-powered insights and comprehensive data aggregation
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class CustomerProfilesInterface:
    """
    Comprehensive customer profiles interface providing 360-degree customer view
    Features unified data aggregation, AI insights, and real-time synchronization
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.selected_customer = None
        self.customers_list = []
        self.search_query = ""
        
        # Sample customer data (in production, this would come from API)
        self.sample_customers = [
            {
                "id": 1,
                "name": "<PERSON>",
                "company": "Biuro Rachunkowe JK",
                "email": "<EMAIL>",
                "phone": "+48 ***********",
                "address": "ul. <PERSON>ł<PERSON>ws<PERSON> 1, 00-001 Warszawa",
                "status": "active",
                "health_score": 85,
                "total_revenue": 45600.00,
                "last_contact": "2024-01-15",
                "equipment_count": 3,
                "service_orders": 12,
                "satisfaction_score": 4.5
            },
            {
                "id": 2,
                "name": "Anna Nowak",
                "company": "Restauracja Smaki",
                "email": "<EMAIL>",
                "phone": "+48 ***********",
                "address": "ul. Nowy Świat 15, 00-029 Warszawa",
                "status": "active",
                "health_score": 92,
                "total_revenue": 78900.00,
                "last_contact": "2024-01-20",
                "equipment_count": 5,
                "service_orders": 18,
                "satisfaction_score": 4.8
            },
            {
                "id": 3,
                "name": "Piotr Wiśniewski",
                "company": "Hotel Centrum",
                "email": "<EMAIL>",
                "phone": "+48 ***********",
                "address": "ul. Krakowskie Przedmieście 5, 00-068 Warszawa",
                "status": "at_risk",
                "health_score": 45,
                "total_revenue": 125000.00,
                "last_contact": "2023-12-10",
                "equipment_count": 12,
                "service_orders": 35,
                "satisfaction_score": 3.2
            }
        ]
        
        logger.info("👥 Customer Profiles Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main customer profiles interface"""
        
        # Header with search and filters
        header = self._create_header()
        
        # Main content area with customer list and profile view
        content_area = self._create_content_area()
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                content_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create header with search and action buttons"""
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "👥 Customer Profiles",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.PRIMARY
                    ),
                    ft.Text(
                        "360° Customer Intelligence & Management",
                        size=14,
                        color=ft.colors.ON_SURFACE_VARIANT
                    )
                ], spacing=5),
                ft.Container(expand=True),
                ft.Row([
                    ft.TextField(
                        hint_text="Search customers...",
                        width=300,
                        prefix_icon=ft.icons.SEARCH,
                        on_change=self._on_search_change,
                        border_radius=25
                    ),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.icons.PERSON_ADD),
                            ft.Text("New Customer")
                        ], spacing=8),
                        on_click=self._create_new_customer
                    ),
                    ft.IconButton(
                        icon=ft.icons.REFRESH,
                        tooltip="Refresh Data",
                        on_click=self._refresh_data
                    )
                ], spacing=10)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_content_area(self) -> ft.Row:
        """Create main content area with customer list and profile view"""
        
        # Customer list panel
        customer_list_panel = self._create_customer_list_panel()
        
        # Customer profile panel
        customer_profile_panel = self._create_customer_profile_panel()
        
        return ft.Row([
            customer_list_panel,
            ft.Container(width=15),
            customer_profile_panel
        ], expand=True)
    
    def _create_customer_list_panel(self) -> ft.Container:
        """Create customer list panel with filtering and sorting"""
        
        # Filter chips
        filter_chips = ft.Row([
            ft.FilterChip(
                label=ft.Text("All"),
                selected=True,
                on_click=lambda e: self._filter_customers("all")
            ),
            ft.FilterChip(
                label=ft.Text("Active"),
                on_click=lambda e: self._filter_customers("active")
            ),
            ft.FilterChip(
                label=ft.Text("At Risk"),
                on_click=lambda e: self._filter_customers("at_risk")
            ),
            ft.FilterChip(
                label=ft.Text("High Value"),
                on_click=lambda e: self._filter_customers("high_value")
            )
        ], spacing=8, scroll=ft.ScrollMode.AUTO)
        
        # Customer cards
        customer_cards = ft.Column(
            [self._create_customer_card(customer) for customer in self.sample_customers],
            spacing=10,
            scroll=ft.ScrollMode.AUTO
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Text("Customer List", size=16, weight=ft.FontWeight.BOLD),
                filter_chips,
                ft.Divider(height=10, color=ft.colors.TRANSPARENT),
                customer_cards
            ], spacing=10),
            width=350,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_customer_card(self, customer: Dict) -> ft.Container:
        """Create individual customer card"""
        
        # Status color
        status_color = {
            "active": ft.colors.GREEN,
            "at_risk": ft.colors.ORANGE,
            "inactive": ft.colors.RED
        }.get(customer["status"], ft.colors.GREY)
        
        # Health score color
        health_color = ft.colors.GREEN if customer["health_score"] >= 70 else \
                     ft.colors.ORANGE if customer["health_score"] >= 50 else ft.colors.RED
        
        card = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.CircleAvatar(
                        content=ft.Text(customer["name"][:2].upper()),
                        bgcolor=ft.colors.PRIMARY,
                        color=ft.colors.ON_PRIMARY,
                        radius=20
                    ),
                    ft.Column([
                        ft.Text(
                            customer["name"],
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            overflow=ft.TextOverflow.ELLIPSIS
                        ),
                        ft.Text(
                            customer["company"],
                            size=12,
                            color=ft.colors.ON_SURFACE_VARIANT,
                            overflow=ft.TextOverflow.ELLIPSIS
                        )
                    ], spacing=2, expand=True),
                    ft.Container(
                        content=ft.Text(
                            f"{customer['health_score']}%",
                            size=10,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=health_color,
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        border_radius=10
                    )
                ], spacing=10),
                ft.Row([
                    ft.Icon(ft.icons.CIRCLE, color=status_color, size=8),
                    ft.Text(customer["status"].replace("_", " ").title(), size=10),
                    ft.Container(expand=True),
                    ft.Text(f"${customer['total_revenue']:,.0f}", size=10, weight=ft.FontWeight.BOLD)
                ], spacing=5),
                ft.Row([
                    ft.Icon(ft.icons.SETTINGS, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(f"{customer['equipment_count']} units", size=10),
                    ft.Container(expand=True),
                    ft.Icon(ft.icons.ASSIGNMENT, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(f"{customer['service_orders']} orders", size=10)
                ], spacing=5)
            ], spacing=8),
            padding=ft.padding.all(12),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_click=lambda e, customer=customer: self._select_customer(customer),
            ink=True
        )
        
        return card
    
    def _create_customer_profile_panel(self) -> ft.Container:
        """Create detailed customer profile panel"""
        
        if not self.selected_customer:
            # Empty state
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.icons.PERSON_OUTLINE, size=64, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        "Select a customer to view profile",
                        size=16,
                        color=ft.colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], 
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.CENTER),
                expand=True,
                padding=ft.padding.all(20),
                bgcolor=ft.colors.SURFACE,
                border_radius=12,
                alignment=ft.alignment.center
            )
        
        # Customer profile content
        customer = self.selected_customer
        
        # Profile header
        profile_header = self._create_profile_header(customer)
        
        # Profile tabs
        profile_tabs = self._create_profile_tabs(customer)
        
        return ft.Container(
            content=ft.Column([
                profile_header,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                profile_tabs
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            expand=True,
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_profile_header(self, customer: Dict) -> ft.Container:
        """Create customer profile header"""
        
        # Health score color
        health_color = ft.colors.GREEN if customer["health_score"] >= 70 else \
                     ft.colors.ORANGE if customer["health_score"] >= 50 else ft.colors.RED
        
        return ft.Container(
            content=ft.Row([
                ft.CircleAvatar(
                    content=ft.Text(customer["name"][:2].upper()),
                    bgcolor=ft.colors.PRIMARY,
                    color=ft.colors.ON_PRIMARY,
                    radius=30
                ),
                ft.Column([
                    ft.Text(
                        customer["name"],
                        size=20,
                        weight=ft.FontWeight.BOLD
                    ),
                    ft.Text(
                        customer["company"],
                        size=14,
                        color=ft.colors.ON_SURFACE_VARIANT
                    ),
                    ft.Row([
                        ft.Icon(ft.icons.EMAIL, size=14),
                        ft.Text(customer["email"], size=12)
                    ], spacing=5),
                    ft.Row([
                        ft.Icon(ft.icons.PHONE, size=14),
                        ft.Text(customer["phone"], size=12)
                    ], spacing=5)
                ], spacing=5, expand=True),
                ft.Column([
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                f"{customer['health_score']}%",
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.WHITE
                            ),
                            ft.Text(
                                "Health Score",
                                size=10,
                                color=ft.colors.WHITE
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        bgcolor=health_color,
                        padding=ft.padding.all(10),
                        border_radius=8
                    ),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.EDIT, size=16),
                                ft.Text("Edit")
                            ], spacing=5),
                            on_click=lambda e: self._edit_customer(customer)
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.MESSAGE, size=16),
                                ft.Text("Contact")
                            ], spacing=5),
                            on_click=lambda e: self._contact_customer(customer)
                        )
                    ], spacing=8)
                ], spacing=10, horizontal_alignment=ft.CrossAxisAlignment.END)
            ], spacing=15),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8
        )
    
    def _create_profile_tabs(self, customer: Dict) -> ft.Tabs:
        """Create customer profile tabs with detailed information"""
        
        return ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Overview",
                    icon=ft.icons.DASHBOARD,
                    content=self._create_overview_tab(customer)
                ),
                ft.Tab(
                    text="Equipment",
                    icon=ft.icons.SETTINGS,
                    content=self._create_equipment_tab(customer)
                ),
                ft.Tab(
                    text="Service History",
                    icon=ft.icons.HISTORY,
                    content=self._create_service_history_tab(customer)
                ),
                ft.Tab(
                    text="Communications",
                    icon=ft.icons.CHAT,
                    content=self._create_communications_tab(customer)
                ),
                ft.Tab(
                    text="Financial",
                    icon=ft.icons.ATTACH_MONEY,
                    content=self._create_financial_tab(customer)
                ),
                ft.Tab(
                    text="AI Insights",
                    icon=ft.icons.PSYCHOLOGY,
                    content=self._create_ai_insights_tab(customer)
                )
            ]
        )
    
    def _create_overview_tab(self, customer: Dict) -> ft.Container:
        """Create customer overview tab"""
        
        # Key metrics
        metrics = ft.Row([
            self._create_metric_card("Total Revenue", f"${customer['total_revenue']:,.2f}", ft.icons.ATTACH_MONEY, ft.colors.GREEN),
            self._create_metric_card("Equipment Units", str(customer['equipment_count']), ft.icons.SETTINGS, ft.colors.BLUE),
            self._create_metric_card("Service Orders", str(customer['service_orders']), ft.icons.ASSIGNMENT, ft.colors.ORANGE),
            self._create_metric_card("Satisfaction", f"{customer['satisfaction_score']}/5", ft.icons.STAR, ft.colors.PURPLE)
        ], spacing=15)
        
        # Recent activity
        recent_activity = ft.Container(
            content=ft.Column([
                ft.Text("Recent Activity", size=16, weight=ft.FontWeight.BOLD),
                ft.Column([
                    self._create_activity_item("Service completed - AC maintenance", "2 days ago", ft.icons.CHECK_CIRCLE, ft.colors.GREEN),
                    self._create_activity_item("Invoice paid - $1,250", "1 week ago", ft.icons.PAYMENT, ft.colors.BLUE),
                    self._create_activity_item("Equipment installed - Daikin VRV", "2 weeks ago", ft.icons.BUILD, ft.colors.ORANGE),
                    self._create_activity_item("Contract renewed", "1 month ago", ft.icons.DESCRIPTION, ft.colors.PURPLE)
                ], spacing=8)
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8
        )
        
        return ft.Container(
            content=ft.Column([
                metrics,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                recent_activity
            ], spacing=0),
            padding=ft.padding.all(10)
        )
    
    def _create_metric_card(self, title: str, value: str, icon, color) -> ft.Container:
        """Create metric card for overview"""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, color=color, size=24),
                ft.Text(value, size=18, weight=ft.FontWeight.BOLD),
                ft.Text(title, size=12, color=ft.colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
            width=120,
            height=80,
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT)
        )
    
    def _create_activity_item(self, text: str, time: str, icon, color) -> ft.Row:
        """Create activity item"""
        return ft.Row([
            ft.Icon(icon, color=color, size=16),
            ft.Column([
                ft.Text(text, size=12, weight=ft.FontWeight.W_500),
                ft.Text(time, size=10, color=ft.colors.ON_SURFACE_VARIANT)
            ], spacing=2, expand=True)
        ], spacing=10)
    
    def _create_equipment_tab(self, customer: Dict) -> ft.Container:
        """Create equipment tab - placeholder"""
        return ft.Container(
            content=ft.Text("🔧 Equipment registry coming soon...", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        )
    
    def _create_service_history_tab(self, customer: Dict) -> ft.Container:
        """Create service history tab - placeholder"""
        return ft.Container(
            content=ft.Text("📋 Service history coming soon...", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        )
    
    def _create_communications_tab(self, customer: Dict) -> ft.Container:
        """Create communications tab - placeholder"""
        return ft.Container(
            content=ft.Text("💬 Communications timeline coming soon...", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        )
    
    def _create_financial_tab(self, customer: Dict) -> ft.Container:
        """Create financial tab - placeholder"""
        return ft.Container(
            content=ft.Text("💰 Financial overview coming soon...", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        )
    
    def _create_ai_insights_tab(self, customer: Dict) -> ft.Container:
        """Create AI insights tab - placeholder"""
        return ft.Container(
            content=ft.Text("🤖 AI insights coming soon...", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        )
    
    def _select_customer(self, customer: Dict):
        """Select customer and update profile view"""
        self.selected_customer = customer
        logger.info(f"Selected customer: {customer['name']}")
        if self.page:
            self.page.update()
    
    def _on_search_change(self, e):
        """Handle search input change"""
        self.search_query = e.control.value
        logger.info(f"Search query: {self.search_query}")
        # TODO: Implement search filtering
    
    def _filter_customers(self, filter_type: str):
        """Filter customers by type"""
        logger.info(f"Filter customers by: {filter_type}")
        # TODO: Implement filtering logic
    
    def _create_new_customer(self, e):
        """Create new customer"""
        logger.info("Create new customer clicked")
        # TODO: Implement new customer dialog
    
    def _refresh_data(self, e):
        """Refresh customer data"""
        logger.info("Refreshing customer data...")
        # TODO: Implement data refresh from API
    
    def _edit_customer(self, customer: Dict):
        """Edit customer"""
        logger.info(f"Edit customer: {customer['name']}")
        # TODO: Implement edit customer dialog
    
    def _contact_customer(self, customer: Dict):
        """Contact customer"""
        logger.info(f"Contact customer: {customer['name']}")
        # TODO: Implement contact customer functionality
