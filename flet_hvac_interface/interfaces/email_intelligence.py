"""
📧 Email Intelligence Interface - AI-Powered Communication Hub
Advanced email processing with sentiment analysis, auto-responses, and customer insights
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class EmailIntelligenceInterface:
    """
    AI-powered email intelligence interface for comprehensive communication management
    Features sentiment analysis, auto-categorization, and intelligent response suggestions
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.selected_email = None
        self.email_filter = "all"
        
        # Sample email data (in production, this would come from API)
        self.sample_emails = [
            {
                "id": 1,
                "from": "<EMAIL>",
                "from_name": "<PERSON>",
                "subject": "Pilne - Problem z klimatyzacją",
                "preview": "<PERSON><PERSON><PERSON> dobry, mamy problem z systemem klimatyzacji w biurze...",
                "timestamp": datetime.now() - timedelta(minutes=15),
                "status": "unread",
                "priority": "high",
                "sentiment": "negative",
                "category": "support",
                "ai_summary": "Customer reports urgent AC system malfunction in office",
                "confidence": 0.92,
                "suggested_actions": ["Schedule emergency service", "Contact technician", "Send status update"],
                "customer_id": 1,
                "has_attachments": False
            },
            {
                "id": 2,
                "from": "<EMAIL>",
                "from_name": "Anna Nowak",
                "subject": "Dziękuję za serwis",
                "preview": "Bardzo dziękuję za szybki i profesjonalny serwis...",
                "timestamp": datetime.now() - timedelta(hours=2),
                "status": "read",
                "priority": "normal",
                "sentiment": "positive",
                "category": "feedback",
                "ai_summary": "Customer expressing satisfaction with recent service",
                "confidence": 0.95,
                "suggested_actions": ["Request review", "Send thank you", "Schedule follow-up"],
                "customer_id": 2,
                "has_attachments": False
            },
            {
                "id": 3,
                "from": "<EMAIL>",
                "from_name": "Piotr Wiśniewski",
                "subject": "Oferta na nowy system",
                "preview": "Proszę o przygotowanie oferty na system klimatyzacji...",
                "timestamp": datetime.now() - timedelta(hours=4),
                "status": "read",
                "priority": "normal",
                "sentiment": "neutral",
                "category": "sales",
                "ai_summary": "Request for quotation for new HVAC system installation",
                "confidence": 0.88,
                "suggested_actions": ["Prepare quote", "Schedule site visit", "Send product catalog"],
                "customer_id": 3,
                "has_attachments": True
            }
        ]
        
        logger.info("📧 Email Intelligence Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main email intelligence interface"""
        
        # Header with stats and actions
        header = self._create_header()
        
        # Main content area with email list and preview
        content_area = self._create_content_area()
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                content_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        # Start real-time email monitoring
        if self.threading_manager:
            self.threading_manager.start_thread(
                "email_monitoring",
                self._email_monitoring_worker,
                daemon=True
            )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create header with email stats and actions"""
        
        # Calculate stats
        total_emails = len(self.sample_emails)
        unread_count = len([e for e in self.sample_emails if e["status"] == "unread"])
        high_priority = len([e for e in self.sample_emails if e["priority"] == "high"])
        positive_sentiment = len([e for e in self.sample_emails if e["sentiment"] == "positive"])
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "📧 Email Intelligence",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.PRIMARY
                        ),
                        ft.Text(
                            "AI-Powered Communication Hub",
                            size=14,
                            color=ft.colors.ON_SURFACE_VARIANT
                        )
                    ], spacing=5),
                    ft.Container(expand=True),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.REFRESH),
                                ft.Text("Sync")
                            ], spacing=8),
                            on_click=self._sync_emails
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.SMART_TOY),
                                ft.Text("AI Analysis")
                            ], spacing=8),
                            on_click=self._run_ai_analysis
                        )
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                
                # Stats row
                ft.Row([
                    self._create_stat_card("Total Emails", str(total_emails), ft.icons.EMAIL, ft.colors.BLUE),
                    self._create_stat_card("Unread", str(unread_count), ft.icons.MARK_EMAIL_UNREAD, ft.colors.ORANGE),
                    self._create_stat_card("High Priority", str(high_priority), ft.icons.PRIORITY_HIGH, ft.colors.RED),
                    self._create_stat_card("Positive", str(positive_sentiment), ft.icons.SENTIMENT_SATISFIED, ft.colors.GREEN)
                ], spacing=15)
            ], spacing=0),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_stat_card(self, title: str, value: str, icon, color) -> ft.Container:
        """Create statistics card"""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=color, size=24),
                ft.Column([
                    ft.Text(value, size=18, weight=ft.FontWeight.BOLD),
                    ft.Text(title, size=12, color=ft.colors.ON_SURFACE_VARIANT)
                ], spacing=2)
            ], spacing=10),
            width=150,
            padding=ft.padding.all(12),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT)
        )
    
    def _create_content_area(self) -> ft.Row:
        """Create main content area with email list and preview"""
        
        # Email list panel
        email_list_panel = self._create_email_list_panel()
        
        # Email preview panel
        email_preview_panel = self._create_email_preview_panel()
        
        return ft.Row([
            email_list_panel,
            ft.Container(width=15),
            email_preview_panel
        ], expand=True)
    
    def _create_email_list_panel(self) -> ft.Container:
        """Create email list panel with filtering"""
        
        # Filter chips
        filter_chips = ft.Row([
            ft.FilterChip(
                label=ft.Text("All"),
                selected=self.email_filter == "all",
                on_click=lambda e: self._set_filter("all")
            ),
            ft.FilterChip(
                label=ft.Text("Unread"),
                selected=self.email_filter == "unread",
                on_click=lambda e: self._set_filter("unread")
            ),
            ft.FilterChip(
                label=ft.Text("High Priority"),
                selected=self.email_filter == "high_priority",
                on_click=lambda e: self._set_filter("high_priority")
            ),
            ft.FilterChip(
                label=ft.Text("Support"),
                selected=self.email_filter == "support",
                on_click=lambda e: self._set_filter("support")
            ),
            ft.FilterChip(
                label=ft.Text("Sales"),
                selected=self.email_filter == "sales",
                on_click=lambda e: self._set_filter("sales")
            )
        ], spacing=8, scroll=ft.ScrollMode.AUTO)
        
        # Email list
        email_list = ft.Column(
            [self._create_email_item(email) for email in self._get_filtered_emails()],
            spacing=8,
            scroll=ft.ScrollMode.AUTO
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Text("Email List", size=16, weight=ft.FontWeight.BOLD),
                filter_chips,
                ft.Divider(height=10, color=ft.colors.TRANSPARENT),
                email_list
            ], spacing=10),
            width=400,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_email_item(self, email: Dict) -> ft.Container:
        """Create individual email item"""
        
        # Status indicators
        status_color = ft.colors.PRIMARY if email["status"] == "unread" else ft.colors.ON_SURFACE_VARIANT
        priority_color = {
            "high": ft.colors.RED,
            "normal": ft.colors.BLUE,
            "low": ft.colors.GREEN
        }.get(email["priority"], ft.colors.GREY)
        
        sentiment_icon = {
            "positive": ft.icons.SENTIMENT_SATISFIED,
            "negative": ft.icons.SENTIMENT_DISSATISFIED,
            "neutral": ft.icons.SENTIMENT_NEUTRAL
        }.get(email["sentiment"], ft.icons.HELP)
        
        sentiment_color = {
            "positive": ft.colors.GREEN,
            "negative": ft.colors.RED,
            "neutral": ft.colors.GREY
        }.get(email["sentiment"], ft.colors.GREY)
        
        # Time formatting
        time_diff = datetime.now() - email["timestamp"]
        if time_diff.days > 0:
            time_str = f"{time_diff.days}d ago"
        elif time_diff.seconds > 3600:
            time_str = f"{time_diff.seconds // 3600}h ago"
        else:
            time_str = f"{time_diff.seconds // 60}m ago"
        
        item = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Text(
                            email["from_name"][:2].upper(),
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.WHITE
                        ),
                        width=30,
                        height=30,
                        bgcolor=ft.colors.PRIMARY,
                        border_radius=15,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        ft.Row([
                            ft.Text(
                                email["from_name"],
                                size=13,
                                weight=ft.FontWeight.BOLD if email["status"] == "unread" else ft.FontWeight.NORMAL,
                                color=status_color,
                                overflow=ft.TextOverflow.ELLIPSIS,
                                expand=True
                            ),
                            ft.Text(time_str, size=10, color=ft.colors.ON_SURFACE_VARIANT)
                        ]),
                        ft.Text(
                            email["subject"],
                            size=12,
                            weight=ft.FontWeight.W_500 if email["status"] == "unread" else ft.FontWeight.NORMAL,
                            overflow=ft.TextOverflow.ELLIPSIS
                        ),
                        ft.Text(
                            email["preview"],
                            size=11,
                            color=ft.colors.ON_SURFACE_VARIANT,
                            overflow=ft.TextOverflow.ELLIPSIS,
                            max_lines=2
                        )
                    ], spacing=3, expand=True)
                ], spacing=10),
                
                ft.Row([
                    ft.Container(
                        content=ft.Text(
                            email["category"].upper(),
                            size=9,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=priority_color,
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        border_radius=8
                    ),
                    ft.Icon(sentiment_icon, color=sentiment_color, size=16),
                    ft.Text(f"{email['confidence']:.0%}", size=10, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Container(expand=True),
                    ft.Icon(ft.icons.ATTACHMENT, size=14) if email["has_attachments"] else ft.Container(),
                    ft.Icon(ft.icons.PRIORITY_HIGH, color=ft.colors.RED, size=14) if email["priority"] == "high" else ft.Container()
                ], spacing=5)
            ], spacing=8),
            padding=ft.padding.all(12),
            bgcolor=ft.colors.SURFACE_VARIANT if email["status"] == "unread" else ft.colors.SURFACE,
            border_radius=8,
            border=ft.border.all(
                2 if email == self.selected_email else 1,
                ft.colors.PRIMARY if email == self.selected_email else ft.colors.OUTLINE_VARIANT
            ),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_click=lambda e, email=email: self._select_email(email),
            ink=True
        )
        
        return item
    
    def _create_email_preview_panel(self) -> ft.Container:
        """Create email preview and AI analysis panel"""
        
        if not self.selected_email:
            # Empty state
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.icons.EMAIL_OUTLINED, size=64, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        "Select an email to view details",
                        size=16,
                        color=ft.colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], 
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.CENTER),
                expand=True,
                padding=ft.padding.all(20),
                bgcolor=ft.colors.SURFACE,
                border_radius=12,
                alignment=ft.alignment.center
            )
        
        email = self.selected_email
        
        # Email header
        email_header = self._create_email_header(email)
        
        # AI analysis section
        ai_analysis = self._create_ai_analysis_section(email)
        
        # Suggested actions
        suggested_actions = self._create_suggested_actions(email)
        
        return ft.Container(
            content=ft.Column([
                email_header,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                ai_analysis,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                suggested_actions
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            expand=True,
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_email_header(self, email: Dict) -> ft.Container:
        """Create email header with details"""
        
        sentiment_color = {
            "positive": ft.colors.GREEN,
            "negative": ft.colors.RED,
            "neutral": ft.colors.GREY
        }.get(email["sentiment"], ft.colors.GREY)
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(
                        email["subject"],
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        expand=True
                    ),
                    ft.Container(
                        content=ft.Text(
                            email["sentiment"].upper(),
                            size=10,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=sentiment_color,
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        border_radius=10
                    )
                ]),
                ft.Row([
                    ft.Text(f"From: {email['from_name']} <{email['from']}>", size=12),
                    ft.Container(expand=True),
                    ft.Text(email["timestamp"].strftime("%Y-%m-%d %H:%M"), size=12, color=ft.colors.ON_SURFACE_VARIANT)
                ]),
                ft.Divider(),
                ft.Text(
                    email["preview"] + " Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                    size=14,
                    color=ft.colors.ON_SURFACE
                )
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8
        )
    
    def _create_ai_analysis_section(self, email: Dict) -> ft.Container:
        """Create AI analysis section"""
        
        return ft.Container(
            content=ft.Column([
                ft.Text("🤖 AI Analysis", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Text("Summary:", weight=ft.FontWeight.BOLD, size=12),
                            ft.Text(email["ai_summary"], size=12, expand=True)
                        ]),
                        ft.Row([
                            ft.Text("Category:", weight=ft.FontWeight.BOLD, size=12),
                            ft.Text(email["category"].title(), size=12),
                            ft.Container(expand=True),
                            ft.Text("Confidence:", weight=ft.FontWeight.BOLD, size=12),
                            ft.Text(f"{email['confidence']:.0%}", size=12)
                        ]),
                        ft.Row([
                            ft.Text("Priority:", weight=ft.FontWeight.BOLD, size=12),
                            ft.Text(email["priority"].title(), size=12),
                            ft.Container(expand=True),
                            ft.Text("Sentiment:", weight=ft.FontWeight.BOLD, size=12),
                            ft.Text(email["sentiment"].title(), size=12)
                        ])
                    ], spacing=8),
                    padding=ft.padding.all(12),
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    border_radius=8
                )
            ], spacing=10)
        )
    
    def _create_suggested_actions(self, email: Dict) -> ft.Container:
        """Create suggested actions section"""
        
        action_buttons = []
        for action in email["suggested_actions"]:
            button = ft.ElevatedButton(
                text=action,
                on_click=lambda e, action=action: self._execute_action(action, email)
            )
            action_buttons.append(button)
        
        return ft.Container(
            content=ft.Column([
                ft.Text("⚡ Suggested Actions", size=16, weight=ft.FontWeight.BOLD),
                ft.Row(action_buttons, spacing=10, wrap=True)
            ], spacing=10)
        )
    
    def _get_filtered_emails(self) -> List[Dict]:
        """Get filtered email list based on current filter"""
        if self.email_filter == "all":
            return self.sample_emails
        elif self.email_filter == "unread":
            return [e for e in self.sample_emails if e["status"] == "unread"]
        elif self.email_filter == "high_priority":
            return [e for e in self.sample_emails if e["priority"] == "high"]
        elif self.email_filter == "support":
            return [e for e in self.sample_emails if e["category"] == "support"]
        elif self.email_filter == "sales":
            return [e for e in self.sample_emails if e["category"] == "sales"]
        return self.sample_emails
    
    def _set_filter(self, filter_type: str):
        """Set email filter"""
        self.email_filter = filter_type
        logger.info(f"Email filter set to: {filter_type}")
        if self.page:
            self.page.update()
    
    def _select_email(self, email: Dict):
        """Select email for preview"""
        self.selected_email = email
        # Mark as read
        email["status"] = "read"
        logger.info(f"Selected email: {email['subject']}")
        if self.page:
            self.page.update()
    
    def _sync_emails(self, e):
        """Sync emails from server"""
        logger.info("Syncing emails...")
        # TODO: Implement email sync from server
    
    def _run_ai_analysis(self, e):
        """Run AI analysis on emails"""
        logger.info("Running AI analysis...")
        # TODO: Implement AI analysis
    
    def _execute_action(self, action: str, email: Dict):
        """Execute suggested action"""
        logger.info(f"Executing action: {action} for email: {email['subject']}")
        # TODO: Implement action execution
    
    async def _email_monitoring_worker(self):
        """Background worker for email monitoring"""
        while True:
            try:
                # Simulate email monitoring
                await asyncio.sleep(30)
                logger.debug("Email monitoring tick")
                # TODO: Implement real email monitoring
                
            except Exception as e:
                logger.error(f"Email monitoring error: {e}")
                await asyncio.sleep(60)
