"""
🔧 Service Orders Interface - Kanban Workflow Management
Advanced service order tracking with drag-and-drop Kanban board and AI-powered dispatch
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class ServiceOrdersInterface:
    """
    Service orders interface with Kanban workflow management
    Features drag-and-drop boards, AI-powered dispatch, and real-time updates
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.selected_order = None
        self.view_mode = "kanban"  # kanban or list
        
        # Kanban stages
        self.kanban_stages = [
            {"id": "backlog", "name": "Backlog", "color": ft.colors.GREY},
            {"id": "scheduled", "name": "Scheduled", "color": ft.colors.BLUE},
            {"id": "in_progress", "name": "In Progress", "color": ft.colors.ORANGE},
            {"id": "pending_parts", "name": "Pending Parts", "color": ft.colors.PURPLE},
            {"id": "quality_check", "name": "Quality Check", "color": ft.colors.TEAL},
            {"id": "completed", "name": "Completed", "color": ft.colors.GREEN},
            {"id": "billed", "name": "Billed", "color": ft.colors.INDIGO},
            {"id": "closed", "name": "Closed", "color": ft.colors.GREY_700}
        ]
        
        # Sample service orders
        self.sample_orders = [
            {
                "id": "SO-2024-001",
                "customer_name": "Jan Kowalski",
                "customer_id": 1,
                "title": "AC Maintenance - Office Building",
                "description": "Regular maintenance for Daikin VRV system",
                "priority": "normal",
                "status": "scheduled",
                "assigned_technician": "Tomasz Nowak",
                "scheduled_date": datetime.now() + timedelta(days=1),
                "estimated_duration": 4,
                "equipment_type": "Daikin VRV",
                "location": "ul. Marszałkowska 1, Warszawa",
                "value": 850.00,
                "created_date": datetime.now() - timedelta(days=2),
                "tags": ["maintenance", "daikin", "commercial"]
            },
            {
                "id": "SO-2024-002",
                "customer_name": "Anna Nowak",
                "customer_id": 2,
                "title": "Emergency Repair - Restaurant AC",
                "description": "AC unit not cooling, urgent repair needed",
                "priority": "high",
                "status": "in_progress",
                "assigned_technician": "Marek Kowalczyk",
                "scheduled_date": datetime.now(),
                "estimated_duration": 3,
                "equipment_type": "LG Multi V",
                "location": "ul. Nowy Świat 15, Warszawa",
                "value": 1200.00,
                "created_date": datetime.now() - timedelta(hours=6),
                "tags": ["emergency", "repair", "lg", "restaurant"]
            },
            {
                "id": "SO-2024-003",
                "customer_name": "Piotr Wiśniewski",
                "customer_id": 3,
                "title": "New Installation - Hotel Rooms",
                "description": "Install 8 new AC units in hotel rooms",
                "priority": "normal",
                "status": "pending_parts",
                "assigned_technician": "Paweł Zieliński",
                "scheduled_date": datetime.now() + timedelta(days=5),
                "estimated_duration": 16,
                "equipment_type": "Mitsubishi Electric",
                "location": "ul. Krakowskie Przedmieście 5, Warszawa",
                "value": 15600.00,
                "created_date": datetime.now() - timedelta(days=7),
                "tags": ["installation", "mitsubishi", "hotel", "bulk"]
            },
            {
                "id": "SO-2024-004",
                "customer_name": "Maria Kowalska",
                "customer_id": 4,
                "title": "System Inspection",
                "description": "Annual system inspection and certification",
                "priority": "low",
                "status": "backlog",
                "assigned_technician": None,
                "scheduled_date": None,
                "estimated_duration": 2,
                "equipment_type": "Carrier 30RB",
                "location": "ul. Żurawia 10, Warszawa",
                "value": 450.00,
                "created_date": datetime.now() - timedelta(days=1),
                "tags": ["inspection", "carrier", "certification"]
            }
        ]
        
        logger.info("🔧 Service Orders Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main service orders interface"""
        
        # Header with stats and controls
        header = self._create_header()
        
        # View toggle and filters
        controls = self._create_controls()
        
        # Main content area (Kanban or List view)
        content_area = self._create_content_area()
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                controls,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                content_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create header with service order stats"""
        
        # Calculate stats
        total_orders = len(self.sample_orders)
        in_progress = len([o for o in self.sample_orders if o["status"] == "in_progress"])
        scheduled_today = len([o for o in self.sample_orders if o["scheduled_date"] and o["scheduled_date"].date() == datetime.now().date()])
        high_priority = len([o for o in self.sample_orders if o["priority"] == "high"])
        total_value = sum(o["value"] for o in self.sample_orders)
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "🔧 Service Orders",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.PRIMARY
                        ),
                        ft.Text(
                            "Kanban Workflow Management",
                            size=14,
                            color=ft.colors.ON_SURFACE_VARIANT
                        )
                    ], spacing=5),
                    ft.Container(expand=True),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ADD_TASK),
                                ft.Text("New Order")
                            ], spacing=8),
                            on_click=self._create_new_order
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.SMART_TOY),
                                ft.Text("AI Dispatch")
                            ], spacing=8),
                            on_click=self._run_ai_dispatch
                        )
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                
                # Stats row
                ft.Row([
                    self._create_stat_card("Total Orders", str(total_orders), ft.icons.ASSIGNMENT, ft.colors.BLUE),
                    self._create_stat_card("In Progress", str(in_progress), ft.icons.PLAY_CIRCLE, ft.colors.ORANGE),
                    self._create_stat_card("Today", str(scheduled_today), ft.icons.TODAY, ft.colors.GREEN),
                    self._create_stat_card("High Priority", str(high_priority), ft.icons.PRIORITY_HIGH, ft.colors.RED),
                    self._create_stat_card("Total Value", f"${total_value:,.0f}", ft.icons.ATTACH_MONEY, ft.colors.PURPLE)
                ], spacing=15, scroll=ft.ScrollMode.AUTO)
            ], spacing=0),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_stat_card(self, title: str, value: str, icon, color) -> ft.Container:
        """Create statistics card"""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=color, size=20),
                ft.Column([
                    ft.Text(value, size=16, weight=ft.FontWeight.BOLD),
                    ft.Text(title, size=10, color=ft.colors.ON_SURFACE_VARIANT)
                ], spacing=2)
            ], spacing=8),
            width=140,
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT)
        )
    
    def _create_controls(self) -> ft.Container:
        """Create view controls and filters"""
        
        return ft.Container(
            content=ft.Row([
                # View toggle
                ft.SegmentedButton(
                    selected={self.view_mode},
                    allow_empty_selection=False,
                    segments=[
                        ft.Segment(
                            value="kanban",
                            label=ft.Text("Kanban"),
                            icon=ft.Icon(ft.icons.VIEW_KANBAN)
                        ),
                        ft.Segment(
                            value="list",
                            label=ft.Text("List"),
                            icon=ft.Icon(ft.icons.VIEW_LIST)
                        )
                    ],
                    on_change=self._change_view_mode
                ),
                
                ft.Container(expand=True),
                
                # Filters
                ft.Row([
                    ft.Dropdown(
                        label="Priority",
                        width=120,
                        options=[
                            ft.dropdown.Option("all", "All"),
                            ft.dropdown.Option("high", "High"),
                            ft.dropdown.Option("normal", "Normal"),
                            ft.dropdown.Option("low", "Low")
                        ],
                        value="all"
                    ),
                    ft.Dropdown(
                        label="Technician",
                        width=150,
                        options=[
                            ft.dropdown.Option("all", "All"),
                            ft.dropdown.Option("tomasz", "Tomasz Nowak"),
                            ft.dropdown.Option("marek", "Marek Kowalczyk"),
                            ft.dropdown.Option("pawel", "Paweł Zieliński")
                        ],
                        value="all"
                    ),
                    ft.IconButton(
                        icon=ft.icons.FILTER_LIST,
                        tooltip="More Filters"
                    )
                ], spacing=10)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_content_area(self) -> ft.Container:
        """Create main content area based on view mode"""
        
        if self.view_mode == "kanban":
            return self._create_kanban_view()
        else:
            return self._create_list_view()
    
    def _create_kanban_view(self) -> ft.Container:
        """Create Kanban board view"""
        
        # Create columns for each stage
        kanban_columns = []
        
        for stage in self.kanban_stages:
            # Get orders for this stage
            stage_orders = [o for o in self.sample_orders if o["status"] == stage["id"]]
            
            # Create order cards
            order_cards = []
            for order in stage_orders:
                card = self._create_order_card(order)
                order_cards.append(card)
            
            # Create stage column
            column = ft.Container(
                content=ft.Column([
                    # Stage header
                    ft.Container(
                        content=ft.Row([
                            ft.Text(
                                stage["name"],
                                size=14,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.WHITE
                            ),
                            ft.Container(expand=True),
                            ft.Container(
                                content=ft.Text(
                                    str(len(stage_orders)),
                                    size=12,
                                    color=ft.colors.WHITE,
                                    weight=ft.FontWeight.BOLD
                                ),
                                bgcolor=ft.colors.with_opacity(0.3, ft.colors.WHITE),
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                border_radius=10
                            )
                        ]),
                        bgcolor=stage["color"],
                        padding=ft.padding.all(12),
                        border_radius=ft.border_radius.only(top_left=8, top_right=8)
                    ),
                    
                    # Order cards
                    ft.Container(
                        content=ft.Column(
                            order_cards,
                            spacing=8,
                            scroll=ft.ScrollMode.AUTO
                        ),
                        padding=ft.padding.all(8),
                        bgcolor=ft.colors.SURFACE_VARIANT,
                        border_radius=ft.border_radius.only(bottom_left=8, bottom_right=8),
                        height=500
                    )
                ], spacing=0),
                width=280,
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=4,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                )
            )
            
            kanban_columns.append(column)
        
        return ft.Container(
            content=ft.Row(
                kanban_columns,
                spacing=15,
                scroll=ft.ScrollMode.AUTO,
                alignment=ft.MainAxisAlignment.START
            ),
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_order_card(self, order: Dict) -> ft.Container:
        """Create service order card for Kanban board"""
        
        # Priority color
        priority_color = {
            "high": ft.colors.RED,
            "normal": ft.colors.BLUE,
            "low": ft.colors.GREEN
        }.get(order["priority"], ft.colors.GREY)
        
        # Format scheduled date
        scheduled_text = "Not scheduled"
        if order["scheduled_date"]:
            if order["scheduled_date"].date() == datetime.now().date():
                scheduled_text = "Today"
            elif order["scheduled_date"].date() == (datetime.now() + timedelta(days=1)).date():
                scheduled_text = "Tomorrow"
            else:
                scheduled_text = order["scheduled_date"].strftime("%m/%d")
        
        card = ft.Container(
            content=ft.Column([
                # Header with ID and priority
                ft.Row([
                    ft.Text(
                        order["id"],
                        size=11,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.PRIMARY
                    ),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text(
                            order["priority"].upper(),
                            size=8,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=priority_color,
                        padding=ft.padding.symmetric(horizontal=4, vertical=2),
                        border_radius=6
                    )
                ]),
                
                # Title and customer
                ft.Text(
                    order["title"],
                    size=12,
                    weight=ft.FontWeight.W_500,
                    max_lines=2,
                    overflow=ft.TextOverflow.ELLIPSIS
                ),
                ft.Text(
                    order["customer_name"],
                    size=10,
                    color=ft.colors.ON_SURFACE_VARIANT
                ),
                
                # Equipment and value
                ft.Row([
                    ft.Icon(ft.icons.SETTINGS, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(order["equipment_type"], size=10, expand=True, overflow=ft.TextOverflow.ELLIPSIS),
                ], spacing=4),
                
                ft.Row([
                    ft.Icon(ft.icons.ATTACH_MONEY, size=12, color=ft.colors.GREEN),
                    ft.Text(f"${order['value']:,.0f}", size=10, weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.Icon(ft.icons.SCHEDULE, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(scheduled_text, size=10)
                ], spacing=4),
                
                # Technician
                ft.Row([
                    ft.Icon(ft.icons.PERSON, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        order["assigned_technician"] or "Unassigned",
                        size=10,
                        color=ft.colors.ON_SURFACE_VARIANT if order["assigned_technician"] else ft.colors.ERROR
                    )
                ], spacing=4) if order.get("assigned_technician") or order["status"] != "backlog" else ft.Container()
            ], spacing=6),
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_click=lambda e, order=order: self._select_order(order),
            ink=True
        )
        
        return card
    
    def _create_list_view(self) -> ft.Container:
        """Create list view of service orders"""
        
        # Table headers
        headers = ft.Row([
            ft.Text("Order ID", size=12, weight=ft.FontWeight.BOLD, width=100),
            ft.Text("Customer", size=12, weight=ft.FontWeight.BOLD, width=150),
            ft.Text("Title", size=12, weight=ft.FontWeight.BOLD, expand=True),
            ft.Text("Status", size=12, weight=ft.FontWeight.BOLD, width=120),
            ft.Text("Technician", size=12, weight=ft.FontWeight.BOLD, width=120),
            ft.Text("Scheduled", size=12, weight=ft.FontWeight.BOLD, width=100),
            ft.Text("Value", size=12, weight=ft.FontWeight.BOLD, width=80),
            ft.Text("Actions", size=12, weight=ft.FontWeight.BOLD, width=80)
        ], spacing=10)
        
        # Order rows
        order_rows = []
        for order in self.sample_orders:
            row = self._create_order_row(order)
            order_rows.append(row)
        
        return ft.Container(
            content=ft.Column([
                headers,
                ft.Divider(),
                ft.Column(
                    order_rows,
                    spacing=5,
                    scroll=ft.ScrollMode.AUTO
                )
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            height=500
        )
    
    def _create_order_row(self, order: Dict) -> ft.Container:
        """Create order row for list view"""
        
        # Status color
        status_color = next((s["color"] for s in self.kanban_stages if s["id"] == order["status"]), ft.colors.GREY)
        
        # Format scheduled date
        scheduled_text = "Not scheduled"
        if order["scheduled_date"]:
            scheduled_text = order["scheduled_date"].strftime("%m/%d")
        
        return ft.Container(
            content=ft.Row([
                ft.Text(order["id"], size=11, width=100),
                ft.Text(order["customer_name"], size=11, width=150, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Text(order["title"], size=11, expand=True, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Container(
                    content=ft.Text(
                        order["status"].replace("_", " ").title(),
                        size=10,
                        color=ft.colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor=status_color,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    border_radius=8,
                    width=120
                ),
                ft.Text(order["assigned_technician"] or "Unassigned", size=11, width=120, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Text(scheduled_text, size=11, width=100),
                ft.Text(f"${order['value']:,.0f}", size=11, width=80),
                ft.IconButton(
                    icon=ft.icons.MORE_VERT,
                    icon_size=16,
                    width=80,
                    on_click=lambda e, order=order: self._show_order_menu(order)
                )
            ], spacing=10),
            padding=ft.padding.symmetric(vertical=8, horizontal=5),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=4,
            on_click=lambda e, order=order: self._select_order(order),
            ink=True
        )
    
    def _change_view_mode(self, e):
        """Change view mode between Kanban and List"""
        self.view_mode = e.control.selected.pop()
        logger.info(f"View mode changed to: {self.view_mode}")
        if self.page:
            self.page.update()
    
    def _select_order(self, order: Dict):
        """Select service order"""
        self.selected_order = order
        logger.info(f"Selected order: {order['id']}")
        # TODO: Show order details panel
    
    def _create_new_order(self, e):
        """Create new service order"""
        logger.info("Create new order clicked")
        # TODO: Implement new order dialog
    
    def _run_ai_dispatch(self, e):
        """Run AI-powered dispatch optimization"""
        logger.info("Running AI dispatch...")
        # TODO: Implement AI dispatch logic
    
    def _show_order_menu(self, order: Dict):
        """Show order context menu"""
        logger.info(f"Show menu for order: {order['id']}")
        # TODO: Implement context menu
