"""
🔧 Equipment Registry Interface - HVAC Equipment Lifecycle Management
Comprehensive equipment tracking with maintenance schedules and performance analytics
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class EquipmentRegistryInterface:
    """
    Equipment registry interface for comprehensive HVAC equipment management
    Features lifecycle tracking, maintenance scheduling, and performance analytics
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.selected_equipment = None
        self.view_mode = "grid"  # grid or list
        self.filter_brand = "all"
        self.filter_status = "all"
        
        # Sample equipment data
        self.sample_equipment = [
            {
                "id": "EQ-2024-001",
                "name": "Daikin VRV IV-S",
                "brand": "Daikin",
                "model": "RXYSQ8T7V1B",
                "type": "VRV System",
                "customer_name": "<PERSON>",
                "customer_id": 1,
                "location": "ul. <PERSON>łkowska 1, Warszawa",
                "installation_date": datetime(2023, 3, 15),
                "warranty_expiry": datetime(2026, 3, 15),
                "status": "active",
                "health_score": 92,
                "last_service": datetime(2024, 1, 10),
                "next_service": datetime(2024, 4, 10),
                "capacity": "22.4 kW",
                "refrigerant": "R-32",
                "serial_number": "DK2023VRV001",
                "maintenance_cost_ytd": 1250.00,
                "energy_efficiency": "A+++",
                "operating_hours": 8760,
                "alerts": []
            },
            {
                "id": "EQ-2024-002",
                "name": "LG Multi V 5",
                "brand": "LG",
                "model": "ARUB360LTE5",
                "type": "Multi V System",
                "customer_name": "Anna Nowak",
                "customer_id": 2,
                "location": "ul. Nowy Świat 15, Warszawa",
                "installation_date": datetime(2022, 8, 20),
                "warranty_expiry": datetime(2025, 8, 20),
                "status": "maintenance_due",
                "health_score": 78,
                "last_service": datetime(2023, 11, 15),
                "next_service": datetime(2024, 2, 15),
                "capacity": "36.0 kW",
                "refrigerant": "R-410A",
                "serial_number": "LG2022MV002",
                "maintenance_cost_ytd": 2100.00,
                "energy_efficiency": "A++",
                "operating_hours": 12450,
                "alerts": ["Maintenance overdue", "Filter replacement needed"]
            },
            {
                "id": "EQ-2024-003",
                "name": "Mitsubishi Electric City Multi",
                "brand": "Mitsubishi",
                "model": "PUMY-P140VKM",
                "type": "City Multi System",
                "customer_name": "Piotr Wiśniewski",
                "customer_id": 3,
                "location": "ul. Krakowskie Przedmieście 5, Warszawa",
                "installation_date": datetime(2021, 5, 10),
                "warranty_expiry": datetime(2024, 5, 10),
                "status": "critical",
                "health_score": 45,
                "last_service": datetime(2023, 12, 5),
                "next_service": datetime(2024, 1, 20),
                "capacity": "14.0 kW",
                "refrigerant": "R-32",
                "serial_number": "ME2021CM003",
                "maintenance_cost_ytd": 3200.00,
                "energy_efficiency": "A+",
                "operating_hours": 18200,
                "alerts": ["Refrigerant leak detected", "Compressor efficiency low", "Urgent service required"]
            },
            {
                "id": "EQ-2024-004",
                "name": "Carrier 30RB Chiller",
                "brand": "Carrier",
                "model": "30RB0302",
                "type": "Air-Cooled Chiller",
                "customer_name": "Maria Kowalska",
                "customer_id": 4,
                "location": "ul. Żurawia 10, Warszawa",
                "installation_date": datetime(2020, 11, 25),
                "warranty_expiry": datetime(2023, 11, 25),
                "status": "active",
                "health_score": 85,
                "last_service": datetime(2024, 1, 5),
                "next_service": datetime(2024, 7, 5),
                "capacity": "302 kW",
                "refrigerant": "R-134a",
                "serial_number": "CR2020CH004",
                "maintenance_cost_ytd": 1800.00,
                "energy_efficiency": "A",
                "operating_hours": 15600,
                "alerts": []
            }
        ]
        
        logger.info("🔧 Equipment Registry Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main equipment registry interface"""
        
        # Header with stats and controls
        header = self._create_header()
        
        # Filters and view controls
        controls = self._create_controls()
        
        # Main content area
        content_area = self._create_content_area()
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                controls,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                content_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create header with equipment stats"""
        
        # Calculate stats
        total_equipment = len(self.sample_equipment)
        active_count = len([e for e in self.sample_equipment if e["status"] == "active"])
        maintenance_due = len([e for e in self.sample_equipment if e["status"] == "maintenance_due"])
        critical_count = len([e for e in self.sample_equipment if e["status"] == "critical"])
        avg_health = sum(e["health_score"] for e in self.sample_equipment) / len(self.sample_equipment)
        total_maintenance_cost = sum(e["maintenance_cost_ytd"] for e in self.sample_equipment)
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "🔧 Equipment Registry",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.PRIMARY
                        ),
                        ft.Text(
                            "HVAC Equipment Lifecycle Management",
                            size=14,
                            color=ft.colors.ON_SURFACE_VARIANT
                        )
                    ], spacing=5),
                    ft.Container(expand=True),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ADD),
                                ft.Text("Add Equipment")
                            ], spacing=8),
                            on_click=self._add_equipment
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ANALYTICS),
                                ft.Text("Analytics")
                            ], spacing=8),
                            on_click=self._show_analytics
                        )
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                
                # Stats row
                ft.Row([
                    self._create_stat_card("Total Equipment", str(total_equipment), ft.icons.SETTINGS, ft.colors.BLUE),
                    self._create_stat_card("Active", str(active_count), ft.icons.CHECK_CIRCLE, ft.colors.GREEN),
                    self._create_stat_card("Maintenance Due", str(maintenance_due), ft.icons.WARNING, ft.colors.ORANGE),
                    self._create_stat_card("Critical", str(critical_count), ft.icons.ERROR, ft.colors.RED),
                    self._create_stat_card("Avg Health", f"{avg_health:.0f}%", ft.icons.FAVORITE, ft.colors.PINK),
                    self._create_stat_card("YTD Cost", f"${total_maintenance_cost:,.0f}", ft.icons.ATTACH_MONEY, ft.colors.PURPLE)
                ], spacing=15, scroll=ft.ScrollMode.AUTO)
            ], spacing=0),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_stat_card(self, title: str, value: str, icon, color) -> ft.Container:
        """Create statistics card"""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=color, size=20),
                ft.Column([
                    ft.Text(value, size=14, weight=ft.FontWeight.BOLD),
                    ft.Text(title, size=10, color=ft.colors.ON_SURFACE_VARIANT)
                ], spacing=2)
            ], spacing=8),
            width=130,
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT)
        )
    
    def _create_controls(self) -> ft.Container:
        """Create view controls and filters"""
        
        return ft.Container(
            content=ft.Row([
                # View toggle
                ft.SegmentedButton(
                    selected={self.view_mode},
                    allow_empty_selection=False,
                    segments=[
                        ft.Segment(
                            value="grid",
                            label=ft.Text("Grid"),
                            icon=ft.Icon(ft.icons.GRID_VIEW)
                        ),
                        ft.Segment(
                            value="list",
                            label=ft.Text("List"),
                            icon=ft.Icon(ft.icons.VIEW_LIST)
                        )
                    ],
                    on_change=self._change_view_mode
                ),
                
                ft.Container(expand=True),
                
                # Filters
                ft.Row([
                    ft.Dropdown(
                        label="Brand",
                        width=120,
                        options=[
                            ft.dropdown.Option("all", "All Brands"),
                            ft.dropdown.Option("daikin", "Daikin"),
                            ft.dropdown.Option("lg", "LG"),
                            ft.dropdown.Option("mitsubishi", "Mitsubishi"),
                            ft.dropdown.Option("carrier", "Carrier")
                        ],
                        value=self.filter_brand,
                        on_change=self._filter_by_brand
                    ),
                    ft.Dropdown(
                        label="Status",
                        width=140,
                        options=[
                            ft.dropdown.Option("all", "All Status"),
                            ft.dropdown.Option("active", "Active"),
                            ft.dropdown.Option("maintenance_due", "Maintenance Due"),
                            ft.dropdown.Option("critical", "Critical"),
                            ft.dropdown.Option("inactive", "Inactive")
                        ],
                        value=self.filter_status,
                        on_change=self._filter_by_status
                    ),
                    ft.TextField(
                        hint_text="Search equipment...",
                        width=200,
                        prefix_icon=ft.icons.SEARCH,
                        border_radius=25
                    )
                ], spacing=10)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_content_area(self) -> ft.Container:
        """Create main content area based on view mode"""
        
        if self.view_mode == "grid":
            return self._create_grid_view()
        else:
            return self._create_list_view()
    
    def _create_grid_view(self) -> ft.Container:
        """Create grid view of equipment"""
        
        # Filter equipment
        filtered_equipment = self._get_filtered_equipment()
        
        # Create equipment cards
        equipment_cards = []
        for equipment in filtered_equipment:
            card = self._create_equipment_card(equipment)
            equipment_cards.append(card)
        
        # Create grid layout
        rows = []
        for i in range(0, len(equipment_cards), 3):  # 3 cards per row
            row_cards = equipment_cards[i:i+3]
            # Pad with empty containers if needed
            while len(row_cards) < 3:
                row_cards.append(ft.Container(width=300))
            
            row = ft.Row(row_cards, spacing=15, alignment=ft.MainAxisAlignment.START)
            rows.append(row)
        
        return ft.Container(
            content=ft.Column(
                rows,
                spacing=15,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            height=600
        )
    
    def _create_equipment_card(self, equipment: Dict) -> ft.Container:
        """Create equipment card for grid view"""
        
        # Status color
        status_colors = {
            "active": ft.colors.GREEN,
            "maintenance_due": ft.colors.ORANGE,
            "critical": ft.colors.RED,
            "inactive": ft.colors.GREY
        }
        status_color = status_colors.get(equipment["status"], ft.colors.GREY)
        
        # Health score color
        health_score = equipment["health_score"]
        health_color = ft.colors.GREEN if health_score >= 80 else \
                     ft.colors.ORANGE if health_score >= 60 else ft.colors.RED
        
        # Brand logo placeholder
        brand_colors = {
            "Daikin": ft.colors.BLUE,
            "LG": ft.colors.RED,
            "Mitsubishi": ft.colors.PURPLE,
            "Carrier": ft.colors.GREEN
        }
        brand_color = brand_colors.get(equipment["brand"], ft.colors.GREY)
        
        card = ft.Container(
            content=ft.Column([
                # Header with brand and status
                ft.Row([
                    ft.Container(
                        content=ft.Text(
                            equipment["brand"][:2].upper(),
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.WHITE
                        ),
                        width=30,
                        height=30,
                        bgcolor=brand_color,
                        border_radius=15,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        ft.Text(
                            equipment["name"],
                            size=13,
                            weight=ft.FontWeight.BOLD,
                            overflow=ft.TextOverflow.ELLIPSIS
                        ),
                        ft.Text(
                            equipment["model"],
                            size=10,
                            color=ft.colors.ON_SURFACE_VARIANT,
                            overflow=ft.TextOverflow.ELLIPSIS
                        )
                    ], spacing=2, expand=True),
                    ft.Container(
                        content=ft.Text(
                            equipment["status"].replace("_", " ").title(),
                            size=8,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=status_color,
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        border_radius=8
                    )
                ], spacing=10),
                
                # Customer and location
                ft.Row([
                    ft.Icon(ft.icons.PERSON, size=14, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        equipment["customer_name"],
                        size=11,
                        expand=True,
                        overflow=ft.TextOverflow.ELLIPSIS
                    )
                ], spacing=5),
                
                ft.Row([
                    ft.Icon(ft.icons.LOCATION_ON, size=14, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        equipment["location"],
                        size=10,
                        color=ft.colors.ON_SURFACE_VARIANT,
                        expand=True,
                        overflow=ft.TextOverflow.ELLIPSIS,
                        max_lines=2
                    )
                ], spacing=5),
                
                # Health score and capacity
                ft.Row([
                    ft.Column([
                        ft.Text("Health Score", size=9, color=ft.colors.ON_SURFACE_VARIANT),
                        ft.Container(
                            content=ft.Text(
                                f"{health_score}%",
                                size=12,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.WHITE
                            ),
                            bgcolor=health_color,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=8,
                            alignment=ft.alignment.center
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    ft.Container(expand=True),
                    ft.Column([
                        ft.Text("Capacity", size=9, color=ft.colors.ON_SURFACE_VARIANT),
                        ft.Text(
                            equipment["capacity"],
                            size=11,
                            weight=ft.FontWeight.BOLD
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
                ]),
                
                # Next service and alerts
                ft.Row([
                    ft.Icon(ft.icons.SCHEDULE, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        f"Next: {equipment['next_service'].strftime('%m/%d/%Y')}",
                        size=10,
                        expand=True
                    ),
                    ft.Icon(
                        ft.icons.WARNING,
                        size=14,
                        color=ft.colors.RED
                    ) if equipment["alerts"] else ft.Container()
                ], spacing=5),
                
                # Alerts count
                ft.Container(
                    content=ft.Text(
                        f"{len(equipment['alerts'])} alerts" if equipment["alerts"] else "No alerts",
                        size=9,
                        color=ft.colors.RED if equipment["alerts"] else ft.colors.GREEN,
                        weight=ft.FontWeight.BOLD
                    ),
                    alignment=ft.alignment.center
                ) if equipment["alerts"] else ft.Container(height=15)
            ], spacing=8),
            width=300,
            height=220,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=12,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=4,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_click=lambda e, equipment=equipment: self._select_equipment(equipment),
            ink=True
        )
        
        return card
    
    def _create_list_view(self) -> ft.Container:
        """Create list view of equipment"""
        
        # Table headers
        headers = ft.Row([
            ft.Text("Equipment", size=12, weight=ft.FontWeight.BOLD, width=200),
            ft.Text("Customer", size=12, weight=ft.FontWeight.BOLD, width=150),
            ft.Text("Status", size=12, weight=ft.FontWeight.BOLD, width=120),
            ft.Text("Health", size=12, weight=ft.FontWeight.BOLD, width=80),
            ft.Text("Next Service", size=12, weight=ft.FontWeight.BOLD, width=100),
            ft.Text("Alerts", size=12, weight=ft.FontWeight.BOLD, width=80),
            ft.Text("Actions", size=12, weight=ft.FontWeight.BOLD, width=80)
        ], spacing=10)
        
        # Equipment rows
        filtered_equipment = self._get_filtered_equipment()
        equipment_rows = []
        for equipment in filtered_equipment:
            row = self._create_equipment_row(equipment)
            equipment_rows.append(row)
        
        return ft.Container(
            content=ft.Column([
                headers,
                ft.Divider(),
                ft.Column(
                    equipment_rows,
                    spacing=5,
                    scroll=ft.ScrollMode.AUTO
                )
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            height=500
        )
    
    def _create_equipment_row(self, equipment: Dict) -> ft.Container:
        """Create equipment row for list view"""
        
        # Status color
        status_colors = {
            "active": ft.colors.GREEN,
            "maintenance_due": ft.colors.ORANGE,
            "critical": ft.colors.RED,
            "inactive": ft.colors.GREY
        }
        status_color = status_colors.get(equipment["status"], ft.colors.GREY)
        
        # Health color
        health_score = equipment["health_score"]
        health_color = ft.colors.GREEN if health_score >= 80 else \
                     ft.colors.ORANGE if health_score >= 60 else ft.colors.RED
        
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(equipment["name"], size=11, weight=ft.FontWeight.BOLD),
                    ft.Text(f"{equipment['brand']} {equipment['model']}", size=9, color=ft.colors.ON_SURFACE_VARIANT)
                ], spacing=2, width=200),
                ft.Text(equipment["customer_name"], size=11, width=150, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Container(
                    content=ft.Text(
                        equipment["status"].replace("_", " ").title(),
                        size=9,
                        color=ft.colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor=status_color,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    border_radius=8,
                    width=120
                ),
                ft.Container(
                    content=ft.Text(
                        f"{health_score}%",
                        size=10,
                        color=ft.colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor=health_color,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    border_radius=8,
                    width=80,
                    alignment=ft.alignment.center
                ),
                ft.Text(equipment["next_service"].strftime("%m/%d/%Y"), size=11, width=100),
                ft.Text(str(len(equipment["alerts"])), size=11, width=80, color=ft.colors.RED if equipment["alerts"] else ft.colors.GREEN),
                ft.IconButton(
                    icon=ft.icons.MORE_VERT,
                    icon_size=16,
                    width=80,
                    on_click=lambda e, equipment=equipment: self._show_equipment_menu(equipment)
                )
            ], spacing=10),
            padding=ft.padding.symmetric(vertical=8, horizontal=5),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=4,
            on_click=lambda e, equipment=equipment: self._select_equipment(equipment),
            ink=True
        )
    
    def _get_filtered_equipment(self) -> List[Dict]:
        """Get filtered equipment list"""
        filtered = self.sample_equipment
        
        if self.filter_brand != "all":
            filtered = [e for e in filtered if e["brand"].lower() == self.filter_brand]
        
        if self.filter_status != "all":
            filtered = [e for e in filtered if e["status"] == self.filter_status]
        
        return filtered
    
    def _change_view_mode(self, e):
        """Change view mode between Grid and List"""
        self.view_mode = e.control.selected.pop()
        logger.info(f"View mode changed to: {self.view_mode}")
        if self.page:
            self.page.update()
    
    def _filter_by_brand(self, e):
        """Filter equipment by brand"""
        self.filter_brand = e.control.value
        logger.info(f"Filter by brand: {self.filter_brand}")
        if self.page:
            self.page.update()
    
    def _filter_by_status(self, e):
        """Filter equipment by status"""
        self.filter_status = e.control.value
        logger.info(f"Filter by status: {self.filter_status}")
        if self.page:
            self.page.update()
    
    def _select_equipment(self, equipment: Dict):
        """Select equipment for detailed view"""
        self.selected_equipment = equipment
        logger.info(f"Selected equipment: {equipment['name']}")
        # TODO: Show equipment details panel
    
    def _add_equipment(self, e):
        """Add new equipment"""
        logger.info("Add equipment clicked")
        # TODO: Implement add equipment dialog
    
    def _show_analytics(self, e):
        """Show equipment analytics"""
        logger.info("Show analytics clicked")
        # TODO: Implement analytics view
    
    def _show_equipment_menu(self, equipment: Dict):
        """Show equipment context menu"""
        logger.info(f"Show menu for equipment: {equipment['name']}")
        # TODO: Implement context menu
