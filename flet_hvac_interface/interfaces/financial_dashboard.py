"""
💰 Financial Dashboard Interface - Business Intelligence & Analytics
Comprehensive financial management with OCR processing and AI-powered insights
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class FinancialDashboardInterface:
    """
    Financial dashboard interface for comprehensive business financial management
    Features OCR invoice processing, payment tracking, and AI-powered financial insights
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.selected_period = "month"  # week, month, quarter, year
        self.selected_invoice = None
        
        # Sample financial data
        self.financial_data = {
            "revenue": {
                "current_month": 45600.00,
                "last_month": 38200.00,
                "ytd": 425000.00,
                "target_month": 50000.00,
                "target_ytd": 600000.00
            },
            "expenses": {
                "current_month": 28400.00,
                "last_month": 24100.00,
                "ytd": 265000.00
            },
            "profit": {
                "current_month": 17200.00,
                "last_month": 14100.00,
                "ytd": 160000.00,
                "margin": 37.7
            },
            "outstanding": {
                "total": 12800.00,
                "overdue": 3200.00,
                "count": 8
            }
        }
        
        # Sample invoices
        self.sample_invoices = [
            {
                "id": "INV-2024-001",
                "customer_name": "Jan Kowalski",
                "customer_id": 1,
                "amount": 1250.00,
                "issue_date": datetime.now() - timedelta(days=5),
                "due_date": datetime.now() + timedelta(days=25),
                "status": "sent",
                "service_order": "SO-2024-001",
                "description": "AC Maintenance - Office Building",
                "payment_method": "bank_transfer",
                "tax_amount": 287.50,
                "net_amount": 962.50
            },
            {
                "id": "INV-2024-002",
                "customer_name": "Anna Nowak",
                "customer_id": 2,
                "amount": 1800.00,
                "issue_date": datetime.now() - timedelta(days=15),
                "due_date": datetime.now() - timedelta(days=1),
                "status": "overdue",
                "service_order": "SO-2024-002",
                "description": "Emergency Repair - Restaurant AC",
                "payment_method": "bank_transfer",
                "tax_amount": 414.00,
                "net_amount": 1386.00
            },
            {
                "id": "INV-2024-003",
                "customer_name": "Piotr Wiśniewski",
                "customer_id": 3,
                "amount": 18600.00,
                "issue_date": datetime.now() - timedelta(days=3),
                "due_date": datetime.now() + timedelta(days=27),
                "status": "draft",
                "service_order": "SO-2024-003",
                "description": "New Installation - Hotel Rooms",
                "payment_method": "bank_transfer",
                "tax_amount": 4278.00,
                "net_amount": 14322.00
            },
            {
                "id": "INV-2024-004",
                "customer_name": "Maria Kowalska",
                "customer_id": 4,
                "amount": 540.00,
                "issue_date": datetime.now() - timedelta(days=20),
                "due_date": datetime.now() - timedelta(days=5),
                "status": "paid",
                "service_order": "SO-2024-004",
                "description": "System Inspection",
                "payment_method": "cash",
                "tax_amount": 124.20,
                "net_amount": 415.80,
                "payment_date": datetime.now() - timedelta(days=3)
            }
        ]
        
        logger.info("💰 Financial Dashboard Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main financial dashboard interface"""
        
        # Header with financial overview
        header = self._create_header()
        
        # Period selector and controls
        controls = self._create_controls()
        
        # Main content area with charts and tables
        content_area = self._create_content_area()
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                controls,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                content_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create header with financial KPIs"""
        
        # Calculate growth percentages
        revenue_growth = ((self.financial_data["revenue"]["current_month"] - 
                          self.financial_data["revenue"]["last_month"]) / 
                         self.financial_data["revenue"]["last_month"]) * 100
        
        profit_growth = ((self.financial_data["profit"]["current_month"] - 
                         self.financial_data["profit"]["last_month"]) / 
                        self.financial_data["profit"]["last_month"]) * 100
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "💰 Financial Dashboard",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.PRIMARY
                        ),
                        ft.Text(
                            "Business Intelligence & Analytics",
                            size=14,
                            color=ft.colors.ON_SURFACE_VARIANT
                        )
                    ], spacing=5),
                    ft.Container(expand=True),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.RECEIPT_LONG),
                                ft.Text("New Invoice")
                            ], spacing=8),
                            on_click=self._create_invoice
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.DOCUMENT_SCANNER),
                                ft.Text("OCR Scan")
                            ], spacing=8),
                            on_click=self._scan_document
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ANALYTICS),
                                ft.Text("Reports")
                            ], spacing=8),
                            on_click=self._generate_reports
                        )
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                
                # KPI Cards
                ft.Row([
                    self._create_kpi_card(
                        "Monthly Revenue",
                        f"${self.financial_data['revenue']['current_month']:,.0f}",
                        f"{revenue_growth:+.1f}%",
                        revenue_growth > 0,
                        ft.icons.TRENDING_UP,
                        ft.colors.GREEN
                    ),
                    self._create_kpi_card(
                        "Monthly Profit",
                        f"${self.financial_data['profit']['current_month']:,.0f}",
                        f"{profit_growth:+.1f}%",
                        profit_growth > 0,
                        ft.icons.ACCOUNT_BALANCE_WALLET,
                        ft.colors.BLUE
                    ),
                    self._create_kpi_card(
                        "Profit Margin",
                        f"{self.financial_data['profit']['margin']:.1f}%",
                        "Target: 40%",
                        self.financial_data['profit']['margin'] >= 35,
                        ft.icons.PERCENT,
                        ft.colors.PURPLE
                    ),
                    self._create_kpi_card(
                        "Outstanding",
                        f"${self.financial_data['outstanding']['total']:,.0f}",
                        f"{self.financial_data['outstanding']['count']} invoices",
                        self.financial_data['outstanding']['overdue'] == 0,
                        ft.icons.PAYMENT,
                        ft.colors.ORANGE
                    ),
                    self._create_kpi_card(
                        "YTD Revenue",
                        f"${self.financial_data['revenue']['ytd']:,.0f}",
                        f"Target: ${self.financial_data['revenue']['target_ytd']:,.0f}",
                        self.financial_data['revenue']['ytd'] >= self.financial_data['revenue']['target_ytd'] * 0.8,
                        ft.icons.CALENDAR_TODAY,
                        ft.colors.TEAL
                    )
                ], spacing=15, scroll=ft.ScrollMode.AUTO)
            ], spacing=0),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_kpi_card(self, title: str, value: str, subtitle: str, positive: bool, icon, color) -> ft.Container:
        """Create KPI card with trend indicator"""
        
        trend_color = ft.colors.GREEN if positive else ft.colors.RED
        trend_icon = ft.icons.TRENDING_UP if positive else ft.icons.TRENDING_DOWN
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(icon, color=color, size=24),
                    ft.Container(expand=True),
                    ft.Icon(trend_icon, color=trend_color, size=16)
                ]),
                ft.Text(
                    value,
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.colors.ON_SURFACE
                ),
                ft.Text(
                    title,
                    size=11,
                    color=ft.colors.ON_SURFACE_VARIANT
                ),
                ft.Text(
                    subtitle,
                    size=10,
                    color=trend_color,
                    weight=ft.FontWeight.W_500
                )
            ], spacing=6),
            width=180,
            height=110,
            padding=ft.padding.all(12),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT)
        )
    
    def _create_controls(self) -> ft.Container:
        """Create period selector and filter controls"""
        
        return ft.Container(
            content=ft.Row([
                # Period selector
                ft.SegmentedButton(
                    selected={self.selected_period},
                    allow_empty_selection=False,
                    segments=[
                        ft.Segment(value="week", label=ft.Text("Week")),
                        ft.Segment(value="month", label=ft.Text("Month")),
                        ft.Segment(value="quarter", label=ft.Text("Quarter")),
                        ft.Segment(value="year", label=ft.Text("Year"))
                    ],
                    on_change=self._change_period
                ),
                
                ft.Container(expand=True),
                
                # Quick actions
                ft.Row([
                    ft.IconButton(
                        icon=ft.icons.REFRESH,
                        tooltip="Refresh Data",
                        on_click=self._refresh_data
                    ),
                    ft.IconButton(
                        icon=ft.icons.DOWNLOAD,
                        tooltip="Export Data",
                        on_click=self._export_data
                    ),
                    ft.IconButton(
                        icon=ft.icons.SETTINGS,
                        tooltip="Settings",
                        on_click=self._show_settings
                    )
                ], spacing=5)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_content_area(self) -> ft.Row:
        """Create main content area with charts and invoice list"""
        
        # Charts section
        charts_section = self._create_charts_section()
        
        # Invoice list section
        invoice_section = self._create_invoice_section()
        
        return ft.Row([
            charts_section,
            ft.Container(width=15),
            invoice_section
        ], expand=True)
    
    def _create_charts_section(self) -> ft.Container:
        """Create charts and analytics section"""
        
        return ft.Container(
            content=ft.Column([
                ft.Text("📊 Financial Analytics", size=16, weight=ft.FontWeight.BOLD),
                
                # Revenue chart placeholder
                ft.Container(
                    content=ft.Column([
                        ft.Text("Revenue Trend", weight=ft.FontWeight.W_500),
                        ft.Container(
                            content=ft.Column([
                                ft.Text("📈", size=48),
                                ft.Text("Revenue Chart", size=14, color=ft.colors.ON_SURFACE_VARIANT),
                                ft.Text("Interactive chart coming soon", size=10, color=ft.colors.ON_SURFACE_VARIANT)
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            height=180,
                            alignment=ft.alignment.center,
                            bgcolor=ft.colors.SURFACE_VARIANT,
                            border_radius=8
                        )
                    ], spacing=10),
                    padding=ft.padding.all(15),
                    bgcolor=ft.colors.SURFACE,
                    border_radius=8
                ),
                
                ft.Divider(height=10, color=ft.colors.TRANSPARENT),
                
                # Expense breakdown
                ft.Container(
                    content=ft.Column([
                        ft.Text("Expense Breakdown", weight=ft.FontWeight.W_500),
                        ft.Container(
                            content=ft.Column([
                                ft.Text("🥧", size=48),
                                ft.Text("Expense Pie Chart", size=14, color=ft.colors.ON_SURFACE_VARIANT),
                                ft.Text("Interactive chart coming soon", size=10, color=ft.colors.ON_SURFACE_VARIANT)
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            height=180,
                            alignment=ft.alignment.center,
                            bgcolor=ft.colors.SURFACE_VARIANT,
                            border_radius=8
                        )
                    ], spacing=10),
                    padding=ft.padding.all(15),
                    bgcolor=ft.colors.SURFACE,
                    border_radius=8
                ),
                
                ft.Divider(height=10, color=ft.colors.TRANSPARENT),
                
                # AI Insights
                ft.Container(
                    content=ft.Column([
                        ft.Text("🤖 AI Financial Insights", weight=ft.FontWeight.W_500),
                        ft.Column([
                            self._create_insight_item("Revenue up 19.4% vs last month", ft.colors.GREEN),
                            self._create_insight_item("Profit margin improved by 2.3%", ft.colors.GREEN),
                            self._create_insight_item("2 invoices are overdue", ft.colors.ORANGE),
                            self._create_insight_item("Q1 target achievable with current trend", ft.colors.BLUE)
                        ], spacing=8)
                    ], spacing=10),
                    padding=ft.padding.all(15),
                    bgcolor=ft.colors.SURFACE,
                    border_radius=8
                )
            ], spacing=15, scroll=ft.ScrollMode.AUTO),
            expand=True
        )
    
    def _create_insight_item(self, text: str, color) -> ft.Row:
        """Create AI insight item"""
        return ft.Row([
            ft.Icon(ft.icons.LIGHTBULB, size=14, color=color),
            ft.Text(text, size=12, expand=True)
        ], spacing=8)
    
    def _create_invoice_section(self) -> ft.Container:
        """Create invoice list section"""
        
        # Filter chips
        filter_chips = ft.Row([
            ft.FilterChip(
                label=ft.Text("All"),
                selected=True,
                on_click=lambda e: self._filter_invoices("all")
            ),
            ft.FilterChip(
                label=ft.Text("Draft"),
                on_click=lambda e: self._filter_invoices("draft")
            ),
            ft.FilterChip(
                label=ft.Text("Sent"),
                on_click=lambda e: self._filter_invoices("sent")
            ),
            ft.FilterChip(
                label=ft.Text("Overdue"),
                on_click=lambda e: self._filter_invoices("overdue")
            ),
            ft.FilterChip(
                label=ft.Text("Paid"),
                on_click=lambda e: self._filter_invoices("paid")
            )
        ], spacing=8, scroll=ft.ScrollMode.AUTO)
        
        # Invoice list
        invoice_list = ft.Column(
            [self._create_invoice_item(invoice) for invoice in self.sample_invoices],
            spacing=8,
            scroll=ft.ScrollMode.AUTO
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Text("📄 Recent Invoices", size=16, weight=ft.FontWeight.BOLD),
                filter_chips,
                ft.Divider(height=10, color=ft.colors.TRANSPARENT),
                invoice_list
            ], spacing=10),
            width=400,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_invoice_item(self, invoice: Dict) -> ft.Container:
        """Create invoice list item"""
        
        # Status color
        status_colors = {
            "draft": ft.colors.GREY,
            "sent": ft.colors.BLUE,
            "overdue": ft.colors.RED,
            "paid": ft.colors.GREEN
        }
        status_color = status_colors.get(invoice["status"], ft.colors.GREY)
        
        # Days until/since due
        days_diff = (invoice["due_date"] - datetime.now()).days
        if invoice["status"] == "paid":
            due_text = "Paid"
            due_color = ft.colors.GREEN
        elif days_diff < 0:
            due_text = f"{abs(days_diff)}d overdue"
            due_color = ft.colors.RED
        elif days_diff == 0:
            due_text = "Due today"
            due_color = ft.colors.ORANGE
        else:
            due_text = f"Due in {days_diff}d"
            due_color = ft.colors.ON_SURFACE_VARIANT
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(
                        invoice["id"],
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.PRIMARY
                    ),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text(
                            invoice["status"].upper(),
                            size=8,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=status_color,
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        border_radius=8
                    )
                ]),
                ft.Text(
                    invoice["customer_name"],
                    size=11,
                    weight=ft.FontWeight.W_500
                ),
                ft.Text(
                    invoice["description"],
                    size=10,
                    color=ft.colors.ON_SURFACE_VARIANT,
                    max_lines=2,
                    overflow=ft.TextOverflow.ELLIPSIS
                ),
                ft.Row([
                    ft.Text(
                        f"${invoice['amount']:,.2f}",
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREEN
                    ),
                    ft.Container(expand=True),
                    ft.Text(
                        due_text,
                        size=10,
                        color=due_color,
                        weight=ft.FontWeight.W_500
                    )
                ])
            ], spacing=6),
            padding=ft.padding.all(12),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_click=lambda e, invoice=invoice: self._select_invoice(invoice),
            ink=True
        )
    
    def _change_period(self, e):
        """Change selected period"""
        self.selected_period = e.control.selected.pop()
        logger.info(f"Period changed to: {self.selected_period}")
        if self.page:
            self.page.update()
    
    def _select_invoice(self, invoice: Dict):
        """Select invoice for detailed view"""
        self.selected_invoice = invoice
        logger.info(f"Selected invoice: {invoice['id']}")
        # TODO: Show invoice details panel
    
    def _filter_invoices(self, filter_type: str):
        """Filter invoices by status"""
        logger.info(f"Filter invoices by: {filter_type}")
        # TODO: Implement invoice filtering
    
    def _create_invoice(self, e):
        """Create new invoice"""
        logger.info("Create invoice clicked")
        # TODO: Implement invoice creation dialog
    
    def _scan_document(self, e):
        """Scan document with OCR"""
        logger.info("OCR scan clicked")
        # TODO: Implement OCR document scanning
    
    def _generate_reports(self, e):
        """Generate financial reports"""
        logger.info("Generate reports clicked")
        # TODO: Implement report generation
    
    def _refresh_data(self, e):
        """Refresh financial data"""
        logger.info("Refreshing financial data...")
        # TODO: Implement data refresh
    
    def _export_data(self, e):
        """Export financial data"""
        logger.info("Export data clicked")
        # TODO: Implement data export
    
    def _show_settings(self, e):
        """Show financial settings"""
        logger.info("Show settings clicked")
        # TODO: Implement settings dialog
