"""
🏠 Dashboard Interface - Central Command Center
Cosmic-level dashboard with real-time KPIs and AI-powered insights
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class DashboardInterface:
    """
    Central dashboard interface providing comprehensive overview of HVAC CRM operations
    Features real-time KPIs, AI insights, and cosmic-level Material Design 3 styling
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.kpi_cards = {}
        self.charts = {}
        self.real_time_data = {
            "active_customers": 247,
            "pending_orders": 18,
            "revenue_today": 15420.50,
            "technicians_active": 8,
            "equipment_monitored": 156,
            "ai_insights_count": 12
        }
        
        logger.info("🏠 Dashboard Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main dashboard interface with cosmic-level design"""
        
        # Header with welcome and real-time status
        header = self._create_header()
        
        # KPI Cards Row
        kpi_row = self._create_kpi_cards()
        
        # Charts and Analytics Section
        analytics_section = self._create_analytics_section()
        
        # Recent Activity and AI Insights
        activity_section = self._create_activity_section()
        
        # Quick Actions Panel
        quick_actions = self._create_quick_actions()
        
        # Main dashboard container with cosmic styling
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                kpi_row,
                ft.Divider(height=30, color=ft.colors.TRANSPARENT),
                analytics_section,
                ft.Divider(height=30, color=ft.colors.TRANSPARENT),
                activity_section,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                quick_actions
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        # Start real-time updates
        if self.threading_manager:
            self.threading_manager.start_thread(
                "dashboard_updates", 
                self._real_time_updates_worker,
                daemon=True
            )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create dashboard header with welcome message and status"""
        current_time = datetime.now().strftime("%H:%M")
        current_date = datetime.now().strftime("%A, %B %d, %Y")
        
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        f"Good {'morning' if datetime.now().hour < 12 else 'afternoon' if datetime.now().hour < 18 else 'evening'}! 🌟",
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.PRIMARY
                    ),
                    ft.Text(
                        f"HVAC CRM Dashboard - {current_date}",
                        size=16,
                        color=ft.colors.ON_SURFACE_VARIANT
                    )
                ], spacing=5),
                ft.Container(expand=True),
                ft.Column([
                    ft.Text(current_time, size=24, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.Icon(ft.icons.CIRCLE, color=ft.colors.GREEN, size=12),
                        ft.Text("System Online", size=12, color=ft.colors.GREEN)
                    ], spacing=5)
                ], horizontal_alignment=ft.CrossAxisAlignment.END)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_kpi_cards(self) -> ft.Row:
        """Create KPI cards with real-time data and cosmic animations"""
        kpi_configs = [
            {
                "title": "Active Customers",
                "value": self.real_time_data["active_customers"],
                "icon": ft.icons.PEOPLE,
                "color": ft.colors.BLUE,
                "trend": "+12%",
                "trend_positive": True
            },
            {
                "title": "Pending Orders", 
                "value": self.real_time_data["pending_orders"],
                "icon": ft.icons.ASSIGNMENT,
                "color": ft.colors.ORANGE,
                "trend": "+3",
                "trend_positive": True
            },
            {
                "title": "Revenue Today",
                "value": f"${self.real_time_data['revenue_today']:,.2f}",
                "icon": ft.icons.ATTACH_MONEY,
                "color": ft.colors.GREEN,
                "trend": "+8.5%",
                "trend_positive": True
            },
            {
                "title": "Active Technicians",
                "value": self.real_time_data["technicians_active"],
                "icon": ft.icons.ENGINEERING,
                "color": ft.colors.PURPLE,
                "trend": "100%",
                "trend_positive": True
            },
            {
                "title": "Equipment Monitored",
                "value": self.real_time_data["equipment_monitored"],
                "icon": ft.icons.SETTINGS,
                "color": ft.colors.TEAL,
                "trend": "+5",
                "trend_positive": True
            },
            {
                "title": "AI Insights",
                "value": self.real_time_data["ai_insights_count"],
                "icon": ft.icons.PSYCHOLOGY,
                "color": ft.colors.PINK,
                "trend": "New",
                "trend_positive": True
            }
        ]
        
        cards = []
        for config in kpi_configs:
            card = self._create_kpi_card(config)
            cards.append(card)
            
        return ft.Row(
            cards,
            spacing=15,
            scroll=ft.ScrollMode.AUTO,
            alignment=ft.MainAxisAlignment.START
        )
    
    def _create_kpi_card(self, config: Dict) -> ft.Container:
        """Create individual KPI card with cosmic styling"""
        trend_color = ft.colors.GREEN if config["trend_positive"] else ft.colors.RED
        trend_icon = ft.icons.TRENDING_UP if config["trend_positive"] else ft.icons.TRENDING_DOWN
        
        card = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(config["icon"], color=config["color"], size=24),
                    ft.Container(expand=True),
                    ft.Icon(trend_icon, color=trend_color, size=16)
                ]),
                ft.Text(
                    str(config["value"]),
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.colors.ON_SURFACE
                ),
                ft.Text(
                    config["title"],
                    size=12,
                    color=ft.colors.ON_SURFACE_VARIANT
                ),
                ft.Text(
                    config["trend"],
                    size=11,
                    color=trend_color,
                    weight=ft.FontWeight.W_500
                )
            ], spacing=8),
            width=180,
            height=120,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_hover=self._on_card_hover
        )
        
        # Store reference for updates
        self.kpi_cards[config["title"]] = card
        
        return card
    
    def _create_analytics_section(self) -> ft.Container:
        """Create analytics section with charts and insights"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "📊 Analytics & Insights",
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.colors.PRIMARY
                ),
                ft.Row([
                    # Revenue Chart Placeholder
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Revenue Trend", weight=ft.FontWeight.W_500),
                            ft.Container(
                                content=ft.Text("📈 Chart Placeholder", size=48),
                                height=200,
                                alignment=ft.alignment.center,
                                bgcolor=ft.colors.SURFACE_VARIANT,
                                border_radius=8
                            )
                        ], spacing=10),
                        expand=True,
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.SURFACE,
                        border_radius=12
                    ),
                    ft.Container(width=15),
                    # Service Orders Chart Placeholder  
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Service Orders", weight=ft.FontWeight.W_500),
                            ft.Container(
                                content=ft.Text("📊 Chart Placeholder", size=48),
                                height=200,
                                alignment=ft.alignment.center,
                                bgcolor=ft.colors.SURFACE_VARIANT,
                                border_radius=8
                            )
                        ], spacing=10),
                        expand=True,
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.SURFACE,
                        border_radius=12
                    )
                ])
            ], spacing=15),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_activity_section(self) -> ft.Row:
        """Create recent activity and AI insights section"""
        # Recent Activity
        recent_activity = ft.Container(
            content=ft.Column([
                ft.Text("🕒 Recent Activity", size=16, weight=ft.FontWeight.BOLD),
                ft.Column([
                    self._create_activity_item("New customer inquiry", "2 min ago", ft.icons.PERSON_ADD),
                    self._create_activity_item("Service order completed", "15 min ago", ft.icons.CHECK_CIRCLE),
                    self._create_activity_item("Equipment maintenance due", "1 hour ago", ft.icons.WARNING),
                    self._create_activity_item("Invoice generated", "2 hours ago", ft.icons.RECEIPT)
                ], spacing=8)
            ], spacing=10),
            expand=True,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
        
        # AI Insights
        ai_insights = ft.Container(
            content=ft.Column([
                ft.Text("🤖 AI Insights", size=16, weight=ft.FontWeight.BOLD),
                ft.Column([
                    self._create_insight_item("Customer satisfaction up 15%", ft.colors.GREEN),
                    self._create_insight_item("Peak demand predicted for next week", ft.colors.BLUE),
                    self._create_insight_item("3 customers at risk of churn", ft.colors.ORANGE),
                    self._create_insight_item("Inventory optimization suggested", ft.colors.PURPLE)
                ], spacing=8)
            ], spacing=10),
            expand=True,
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
        
        return ft.Row([recent_activity, ft.Container(width=15), ai_insights])
    
    def _create_activity_item(self, text: str, time: str, icon) -> ft.Row:
        """Create activity list item"""
        return ft.Row([
            ft.Icon(icon, size=16, color=ft.colors.PRIMARY),
            ft.Column([
                ft.Text(text, size=12, weight=ft.FontWeight.W_500),
                ft.Text(time, size=10, color=ft.colors.ON_SURFACE_VARIANT)
            ], spacing=2),
        ], spacing=10)
    
    def _create_insight_item(self, text: str, color) -> ft.Row:
        """Create AI insight item"""
        return ft.Row([
            ft.Icon(ft.icons.LIGHTBULB, size=16, color=color),
            ft.Text(text, size=12, expand=True)
        ], spacing=10)
    
    def _create_quick_actions(self) -> ft.Container:
        """Create quick actions panel"""
        actions = [
            {"text": "New Customer", "icon": ft.icons.PERSON_ADD, "color": ft.colors.BLUE},
            {"text": "Create Order", "icon": ft.icons.ADD_TASK, "color": ft.colors.GREEN},
            {"text": "Schedule Service", "icon": ft.icons.SCHEDULE, "color": ft.colors.ORANGE},
            {"text": "Generate Report", "icon": ft.icons.ANALYTICS, "color": ft.colors.PURPLE}
        ]
        
        action_buttons = []
        for action in actions:
            button = ft.ElevatedButton(
                content=ft.Row([
                    ft.Icon(action["icon"], color=action["color"]),
                    ft.Text(action["text"])
                ], spacing=8),
                on_click=lambda e, action=action: self._handle_quick_action(action["text"])
            )
            action_buttons.append(button)
        
        return ft.Container(
            content=ft.Column([
                ft.Text("⚡ Quick Actions", size=16, weight=ft.FontWeight.BOLD),
                ft.Row(action_buttons, spacing=10)
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _on_card_hover(self, e):
        """Handle KPI card hover animation"""
        if e.data == "true":  # Mouse enter
            e.control.scale = 1.02
        else:  # Mouse leave
            e.control.scale = 1.0
        e.control.update()
    
    def _handle_quick_action(self, action: str):
        """Handle quick action button clicks"""
        logger.info(f"Quick action triggered: {action}")
        # TODO: Implement navigation to respective interfaces
    
    async def _real_time_updates_worker(self):
        """Background worker for real-time dashboard updates"""
        while True:
            try:
                # Simulate real-time data updates
                await asyncio.sleep(5)
                
                # Update KPI values (simulate changes)
                self.real_time_data["active_customers"] += 1 if datetime.now().second % 10 == 0 else 0
                self.real_time_data["revenue_today"] += 150.75 if datetime.now().second % 15 == 0 else 0
                
                # Update UI if page is available
                if self.page:
                    self.page.update()
                    
            except Exception as e:
                logger.error(f"Dashboard real-time update error: {e}")
                await asyncio.sleep(10)
    
    def refresh_data(self):
        """Refresh dashboard data"""
        logger.info("🔄 Refreshing dashboard data...")
        # TODO: Implement data refresh from APIs
        if self.page:
            self.page.update()
