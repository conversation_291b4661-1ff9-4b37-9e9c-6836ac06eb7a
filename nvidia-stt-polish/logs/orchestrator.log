2025-05-30 14:30:28,908 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:30:38,204 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:30:40,482 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:30:48,487 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:30:51,388 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:30:59,393 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:02,369 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:11,674 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:14,005 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:22,010 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:24,677 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:32,682 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:38,261 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:46,266 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:47,870 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:55,874 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:57,480 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:05,483 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:07,126 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:16,432 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:18,081 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:26,085 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:27,787 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:35,792 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:38,334 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:47,641 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:51,735 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:59,739 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:33:07,026 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:33:15,030 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:33:29,998 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:33:38,002 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:34:05,749 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:34:13,753 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:35:08,443 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:35:16,448 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:36:19,917 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:36:27,921 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:37:31,588 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:37:39,593 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:38:43,442 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:38:51,446 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:39:54,997 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:40:04,325 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:06,551 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:15,887 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:17,803 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:25,807 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:27,496 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:35,501 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:37,126 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:46,467 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:48,235 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:56,240 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:57,997 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:42:06,001 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:42:47,271 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:45:04,162 - __main__ - ERROR - ❌ Błąd inicjalizacji: Timeout connecting to server
2025-05-30 14:51:05,243 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:51:05,247 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 14:51:05,493 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 14:51:05,494 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 14:51:14,820 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:22,823 - __main__ - WARNING - ⚠️ audio_converter: http://audio-converter:8080 - Cannot connect to host audio-converter:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:30,826 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:38,830 - __main__ - WARNING - ⚠️ gemma_integration: http://gemma-integration:8080 - Cannot connect to host gemma-integration:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:50,296 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 14:51:50,297 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 14:51:50,297 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 14:51:50,297 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 14:51:58,300 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:53:08,952 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:54:19,606 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:54:56,596 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:54:56,599 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 14:54:56,850 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 14:54:56,851 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 14:54:56,853 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Connection refused]
2025-05-30 14:54:56,855 - __main__ - WARNING - ⚠️ audio_converter: http://audio-converter:8080 - Cannot connect to host audio-converter:8080 ssl:default [Connection refused]
2025-05-30 14:54:56,856 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:54:56,858 - __main__ - WARNING - ⚠️ gemma_integration: http://gemma-integration:8080 - Cannot connect to host gemma-integration:8080 ssl:default [Connection refused]
2025-05-30 14:55:08,249 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 14:55:08,249 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 14:55:08,249 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 14:55:08,249 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 14:55:08,292 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:56:09,630 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:57:12,393 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:58:23,012 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:59:29,757 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:59:29,760 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 14:59:30,000 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 14:59:30,000 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 14:59:30,002 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Connection refused]
2025-05-30 14:59:30,334 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 14:59:30,336 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:59:30,342 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 14:59:41,995 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 14:59:41,995 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 14:59:41,995 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 14:59:41,995 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 15:01:57,381 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection timed out]
2025-05-30 15:03:08,117 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:04:18,799 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:05:29,385 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:06:40,109 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:07:50,901 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:08:57,757 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 15:10:08,514 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:11:19,165 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:12:29,515 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:13:38,832 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:14:48,550 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:15:58,194 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:17:08,724 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:18:18,733 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:19:28,588 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:20:38,442 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:21:48,568 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:22:58,530 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:24:08,582 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:25:18,753 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:26:29,044 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:27:39,459 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:28:51,150 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:30:04,926 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:32:31,516 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748619151_0
2025-05-30 15:32:31,516 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: 'test_audio.wav'
2025-05-30 15:32:31,517 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748619151_0: [Errno 2] No such file or directory: 'test_audio.wav'
2025-05-30 15:33:54,336 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748619234_0
2025-05-30 15:33:54,341 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: '/home/<USER>/HVAC/unifikacja/nvidia-stt-polish/audio_input/test_audio.wav'
2025-05-30 15:33:54,341 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748619234_0: [Errno 2] No such file or directory: '/home/<USER>/HVAC/unifikacja/nvidia-stt-polish/audio_input/test_audio.wav'
2025-05-30 15:35:27,218 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:40:42,444 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:45:09,019 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 15:45:09,023 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 15:45:09,259 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 15:45:09,260 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 15:45:17,265 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:45:17,341 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 15:45:17,345 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 15:45:17,353 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 15:45:27,828 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 15:45:27,828 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 15:45:27,828 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 15:45:27,828 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 15:45:36,874 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:45:41,749 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748619941_0
2025-05-30 15:45:41,749 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 15:45:41,749 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748619941_0: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 15:46:40,250 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 15:46:40,254 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 15:46:41,919 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 15:46:41,966 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 15:46:49,970 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:46:50,031 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 15:46:50,034 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 15:46:50,039 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 15:47:00,799 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 15:47:00,800 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 15:47:00,800 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 15:47:00,800 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 15:47:07,357 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748620027_0
2025-05-30 15:47:08,938 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:47:16,855 - __main__ - ERROR - ❌ Błąd transkrypcji: Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:47:16,855 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748620027_0: Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:52:26,544 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:57:39,097 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:02:53,184 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:06:32,969 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 16:06:32,972 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 16:06:33,732 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 16:06:33,774 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 16:06:41,778 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 16:06:41,851 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 16:06:41,854 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 16:06:41,863 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 16:06:52,178 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 16:06:52,179 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 16:06:52,179 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 16:06:52,179 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 16:06:52,937 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:08:59,037 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 16:08:59,040 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 16:08:59,792 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 16:08:59,833 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 16:08:59,841 - __main__ - INFO - ✅ nvidia_stt: http://simple-stt:8889
2025-05-30 16:08:59,911 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 16:08:59,914 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 16:08:59,920 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 16:09:10,623 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 16:09:10,623 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 16:09:10,623 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 16:09:10,623 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 16:09:11,356 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:10:13,700 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748621413_0
2025-05-30 16:10:14,126 - __main__ - INFO - ✅ Zadanie zakończone: job_1748621413_0 (0.18s)
2025-05-30 16:12:37,053 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748621557_0
2025-05-30 16:12:37,142 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 16:12:37,142 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748621557_0: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 16:14:26,511 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:19:40,260 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:24:53,959 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-30 16:30:09,181 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:35:23,240 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:40:37,807 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:45:53,694 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:51:08,052 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:56:22,623 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:01:38,771 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 17:06:53,314 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:12:07,774 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:17:24,002 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:22:37,160 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 17:27:50,909 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-30 17:33:06,747 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:38:21,791 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 17:43:36,322 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:48:52,468 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:54:07,223 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 17:59:22,958 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 1, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 18:04:37,270 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 18:09:48,285 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 18:14:57,658 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 18:20:09,403 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 18:21:33,333 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 18:21:33,338 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 18:21:34,229 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 18:21:34,273 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 18:21:34,280 - __main__ - INFO - ✅ nvidia_stt: http://simple-stt:8889
2025-05-30 18:21:34,724 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 18:21:34,726 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 18:21:34,733 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 18:21:45,262 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 18:21:45,264 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 18:21:45,264 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 18:21:45,265 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 18:21:46,076 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 18:26:55,826 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}]}
2025-05-30 18:32:07,139 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 18:37:21,322 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 18:42:35,738 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 18:47:52,865 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.***************}}]}
2025-05-30 18:53:09,141 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.***************}}]}
2025-05-30 18:58:28,004 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}]}
2025-05-30 19:03:44,690 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}]}
2025-05-30 19:09:01,516 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}]}
2025-05-30 19:14:19,239 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 1.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 19:19:35,620 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 19:24:49,834 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 19:30:05,708 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 19:35:20,905 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 19:40:37,283 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 19:45:53,194 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 19:51:04,214 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 19:56:16,202 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 20:01:28,840 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 20:06:40,670 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.**************}}]}
2025-05-30 20:11:52,116 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 20:17:04,231 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 20:22:16,418 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 20:27:28,053 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 20:32:40,668 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 20:37:55,073 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 20:43:10,461 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 20:48:26,549 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 20:53:41,918 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 20:58:58,200 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:04:16,923 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-30 21:09:33,426 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:14:50,966 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:20:09,099 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:25:26,943 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:30:44,994 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:36:02,991 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:41:21,106 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:46:39,294 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 21:51:57,775 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 21:57:16,345 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 22:02:35,256 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 22:07:54,335 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-30 22:13:13,437 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 22:18:32,574 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 22:23:51,727 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 22:29:10,859 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 22:34:30,184 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 22:39:49,504 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 22:45:08,948 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 22:50:28,654 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 22:55:48,249 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 23:01:07,922 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:06:27,466 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:11:47,043 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 23:17:06,783 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 23:22:26,612 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:27:46,537 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:33:06,512 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:38:26,543 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:43:46,540 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:49:06,480 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 23:54:26,452 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 23:59:46,463 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:05:06,589 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:10:26,710 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:15:46,963 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:21:07,367 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:26:29,643 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 2.****************}}]}
2025-05-31 00:31:49,978 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:37:10,233 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 00:42:30,673 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:47:51,140 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:53:11,637 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 00:58:32,325 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 01:03:52,948 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:09:13,633 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:14:34,216 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:19:54,808 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:25:15,481 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:30:36,346 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:35:57,153 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 01:41:18,167 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 01:46:39,162 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 01:52:00,130 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 01:57:21,034 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 02:02:41,911 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:08:02,843 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 02:13:24,024 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:18:45,393 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 02:24:06,620 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:29:27,993 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:34:49,247 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:40:10,532 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 02:45:31,728 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:50:52,955 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 02:56:14,331 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 03:01:35,919 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 03:06:57,395 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 03:12:19,069 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 03:17:40,779 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 03:23:02,470 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 03:28:24,128 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 03:33:45,731 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 03:39:07,435 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 03:44:29,360 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 03:49:51,231 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 03:55:13,150 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 04:00:35,285 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 04:05:57,355 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 04:11:19,432 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 04:16:43,590 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 2.****************}}]}
2025-05-31 04:22:05,591 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 04:27:27,752 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 04:32:50,047 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 04:38:12,281 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 04:43:34,700 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 04:48:57,216 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 04:54:19,643 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 04:59:42,106 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 05:05:04,433 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 05:10:26,984 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 05:15:49,656 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 05:21:12,380 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 05:26:35,184 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 05:31:58,085 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 05:48:04,826 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 05:48:04,831 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-31 05:48:05,721 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-31 05:48:05,765 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-31 05:48:05,770 - __main__ - INFO - ✅ nvidia_stt: http://simple-stt:8889
2025-05-31 05:48:06,181 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-31 05:48:06,183 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-31 05:48:06,185 - __main__ - WARNING - ⚠️ gemma_integration: http://gemma-integration:8080 - Cannot connect to host gemma-integration:8080 ssl:default [Connection refused]
2025-05-31 05:48:17,022 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-31 05:48:17,023 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-31 05:48:17,023 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-31 05:48:17,023 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-31 05:48:17,790 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 05:53:14,096 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 05:58:08,510 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 06:03:00,276 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:07:52,976 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:12:45,439 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:17:36,917 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 06:22:29,230 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 06:27:21,475 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 06:32:12,656 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:37:04,813 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:41:56,941 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 06:46:48,092 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:51:40,229 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 06:56:32,359 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:01:23,590 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 07:06:15,778 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:11:08,075 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 07:15:59,433 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:20:51,759 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 07:25:44,127 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 07:30:35,611 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 07:35:28,098 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:40:20,608 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:45:12,233 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': -0.***************}}]}
2025-05-31 07:50:04,779 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:54:57,340 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 07:59:49,905 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 08:04:41,798 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:09:34,549 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:14:27,404 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 08:19:19,306 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:24:12,175 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:29:05,218 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:33:57,397 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 08:38:50,535 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:43:43,747 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 08:48:36,716 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 08:53:29,334 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 08:55:00,882 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 08:55:00,892 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-31 08:55:01,729 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-31 08:55:01,775 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-31 08:55:01,779 - __main__ - INFO - ✅ nvidia_stt: http://simple-stt:8889
2025-05-31 08:55:02,272 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-31 08:55:02,274 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-31 08:55:02,279 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-31 08:55:12,741 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-31 08:55:12,741 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-31 08:55:12,742 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-31 08:55:12,742 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-31 08:55:13,540 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:00:07,052 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:04:59,851 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 09:09:53,455 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:14:47,119 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:19:39,997 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:24:33,862 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:29:27,936 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 09:34:21,015 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 09:39:14,724 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:44:08,588 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:49:01,708 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:53:55,687 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 09:58:49,674 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.**************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 10:03:43,040 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 10:08:37,105 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 10:13:31,180 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 10:18:24,582 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 10:23:18,785 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 10:28:13,023 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 10:33:06,716 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 10:38:01,176 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 10:42:55,663 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 10:47:49,402 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 10:52:43,833 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 10:57:38,362 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 11:02:32,254 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 11:07:27,015 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:12:21,730 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:17:15,879 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 11:22:10,742 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:27:05,564 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 11:31:59,818 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:36:54,672 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:41:49,677 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:46:44,161 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 11:51:39,292 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-31 11:56:59,485 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 12:02:14,209 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-31 12:07:28,145 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-31 12:11:26,826 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:11:34,834 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:11:37,135 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:11:44,920 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:11:46,441 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:11:54,449 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:11:55,908 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:12:03,911 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:12:05,585 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:12:13,363 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:12:15,844 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:12:23,848 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:12:28,618 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:13:16,772 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:13:24,778 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:13:27,612 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:19:07,996 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:19:16,187 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:19:19,817 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:19:27,822 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:19:31,252 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:19:39,225 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:19:41,671 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:19:49,676 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:19:51,879 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:19:59,884 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:20:02,711 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:20:10,688 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:20:14,930 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:20:22,940 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:20:30,329 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:20:38,334 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:20:52,098 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:21:00,102 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:21:26,514 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:21:34,518 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:22:26,399 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:22:34,402 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:23:35,174 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:23:43,126 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:24:43,861 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:24:51,865 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:25:52,569 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:26:00,573 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:27:01,252 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:27:09,194 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:28:09,880 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:28:17,883 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:29:18,573 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:29:26,577 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:30:27,324 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:30:35,329 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:31:35,991 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:31:43,916 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:32:44,591 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:32:52,595 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:33:53,348 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:34:01,354 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:35:01,984 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:35:09,897 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:36:10,548 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:36:18,552 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:37:19,215 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:37:27,220 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:38:27,960 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:38:35,859 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:39:36,618 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:39:44,623 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:40:45,391 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:40:53,396 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:41:53,992 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:42:01,997 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:43:02,572 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:43:10,452 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:44:11,065 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:44:19,069 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:45:19,801 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:45:27,807 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:46:28,439 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:46:36,322 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:47:36,932 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:47:44,938 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:48:45,694 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:48:53,699 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:49:54,411 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:50:02,416 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:51:03,015 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:51:11,021 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:52:11,775 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:52:19,780 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:53:20,732 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:53:28,736 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:54:29,389 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:54:37,253 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:55:37,895 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:55:45,899 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:56:46,504 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:56:54,509 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:57:55,107 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:58:02,973 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 12:59:03,695 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 12:59:11,700 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 13:00:12,327 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 13:00:20,332 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 13:01:20,930 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 13:01:28,936 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 13:02:29,485 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 13:02:37,347 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 13:03:37,933 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 13:03:45,937 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 13:04:46,539 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 13:04:54,542 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:44:51,384 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:44:59,401 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:45:03,518 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:45:11,526 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:45:14,763 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:45:22,770 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:45:25,595 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:45:33,600 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:45:36,179 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:45:44,183 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:45:46,697 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:45:55,642 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:45:59,842 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:46:07,845 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:46:15,199 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:46:24,319 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:46:38,078 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:46:46,082 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:47:13,562 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:47:21,565 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:48:15,980 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:48:23,984 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:49:27,306 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:49:35,310 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:50:38,768 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:50:46,772 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:51:50,151 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:51:59,395 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:53:01,595 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:53:10,840 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:54:14,408 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:54:22,412 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:55:25,992 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:55:33,996 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:56:38,120 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:56:46,123 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:57:50,439 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:57:58,443 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 17:59:02,587 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 17:59:10,592 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:00:14,774 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:00:24,120 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:01:26,598 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:01:36,182 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:01:37,976 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:01:45,980 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:01:47,788 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:01:55,791 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:01:57,498 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:02:07,095 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:02:08,844 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:02:16,849 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:02:18,500 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:02:26,504 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:02:28,180 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:02:36,184 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:02:39,388 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:02:47,392 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:02:49,771 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:02:57,776 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:03:01,837 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:03:09,841 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:03:18,703 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:03:26,707 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:03:40,319 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:03:49,957 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:03:51,475 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:03:59,479 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:04:01,020 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:04:09,024 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:04:11,021 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:04:20,577 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:04:22,246 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:04:30,251 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:04:32,151 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:04:40,155 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:04:42,311 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:04:50,316 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:04:53,758 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:05:01,762 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:05:04,187 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:05:12,191 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:05:16,196 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:05:24,199 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:05:33,003 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:05:41,008 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:05:54,578 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:06:04,134 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:06:06,084 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:06:14,088 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:06:15,695 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:06:23,699 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:06:25,427 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:06:34,989 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:06:36,894 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:06:44,898 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:06:46,659 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:06:54,663 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:06:56,302 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:07:04,307 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:07:06,601 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:07:16,152 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:07:18,780 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:07:26,785 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:07:29,001 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:07:37,004 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:07:38,989 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:07:48,546 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:07:50,296 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:07:58,300 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:07:59,834 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:08:07,838 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:08:09,493 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:08:19,082 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:08:20,628 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:08:28,632 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:08:30,115 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:08:38,120 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:08:39,571 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:08:47,575 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:08:50,794 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:08:58,799 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:09:01,328 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:09:09,332 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:09:13,387 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:09:21,392 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:09:30,434 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:09:38,438 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:09:52,060 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:10:01,669 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:10:03,180 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:10:11,184 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:10:12,699 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:10:20,703 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:10:22,173 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:10:31,890 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:10:33,484 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:10:41,489 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:10:42,921 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:10:50,925 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:10:52,398 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:11:00,402 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:11:02,078 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:11:11,433 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:11:13,906 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:11:21,909 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:11:25,913 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:11:33,916 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:11:42,747 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:11:50,752 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:12:04,791 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:12:14,361 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:12:16,782 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:12:24,787 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:12:27,383 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:12:35,388 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:12:37,228 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:12:46,860 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:12:48,811 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:12:56,816 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:12:58,351 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:13:06,356 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:13:07,964 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:13:15,968 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:13:17,943 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:13:27,562 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:13:29,139 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:13:37,144 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:13:39,352 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:13:47,357 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:13:49,433 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:13:59,020 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:14:01,257 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:14:09,260 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:14:11,098 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:14:19,102 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:14:20,989 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:14:30,590 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:14:32,261 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:14:40,265 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:14:42,079 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:14:50,083 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:14:51,750 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:14:59,755 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:15:03,087 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:15:11,092 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:15:13,671 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:15:21,675 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:15:26,032 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:15:34,037 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:15:42,944 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:15:50,947 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:16:04,851 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:16:14,499 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:16:16,919 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:16:24,923 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:16:26,998 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:16:35,002 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:16:37,028 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:16:46,637 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:16:48,385 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:16:56,389 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:16:57,906 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:17:05,910 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:17:07,695 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:17:15,699 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:17:19,067 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:17:27,071 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:17:29,470 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:17:37,473 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:17:41,428 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:17:49,432 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:17:58,353 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:18:06,357 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:18:19,927 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:18:29,801 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:18:31,546 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:18:39,550 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:18:41,394 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:18:49,398 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:18:51,305 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:19:01,082 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:19:02,817 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:19:10,821 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:19:12,911 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:19:20,915 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:19:22,912 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:19:30,916 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:19:34,742 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:19:42,746 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:19:45,358 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:19:53,362 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:19:57,466 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:20:05,470 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:20:14,359 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:20:22,363 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:20:35,982 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:20:45,772 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:20:47,345 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:20:55,349 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:20:57,068 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:21:05,072 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:21:06,674 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:21:16,432 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:21:18,066 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:21:26,070 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:21:27,985 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:21:35,989 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:21:37,892 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:21:45,896 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:21:48,015 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:21:57,827 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:21:59,952 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:22:07,957 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:22:09,756 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:22:17,759 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:22:19,460 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:22:29,188 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:22:30,760 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:22:38,764 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:22:40,486 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:22:48,489 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:22:50,173 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:23:00,057 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:23:01,938 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:23:09,942 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:23:11,502 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:23:19,506 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:23:21,414 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:23:29,419 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:23:32,859 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:23:40,863 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:23:43,248 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:23:51,253 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:23:55,251 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:24:03,255 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:24:12,205 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:24:20,208 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:24:33,829 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:24:43,571 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:24:45,547 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:24:53,552 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:24:55,735 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:25:03,739 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:25:05,524 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:25:15,264 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:25:17,277 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:25:25,281 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:25:27,309 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:25:35,313 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:25:37,687 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:25:45,690 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:25:49,725 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:25:57,729 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:26:00,382 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:26:08,387 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:26:12,431 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:26:22,173 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:26:23,572 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:26:31,575 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:26:33,236 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:26:41,240 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:26:43,184 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:26:51,188 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:26:52,890 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:27:02,631 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:27:04,504 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:27:12,508 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:27:14,209 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:27:22,212 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:27:23,807 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:27:33,551 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:27:35,168 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:27:43,171 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:27:44,799 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:27:52,802 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:27:54,629 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:28:04,402 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:28:06,318 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:28:14,322 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:28:15,793 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:28:23,797 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:28:25,987 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:28:33,990 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:28:35,847 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:28:45,686 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:28:47,565 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:28:55,570 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:28:57,958 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:29:05,963 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:29:08,151 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:29:18,037 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:29:19,919 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:29:27,923 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:29:29,609 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:29:37,614 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:29:39,298 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:29:48,961 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:29:50,383 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:29:58,387 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:29:59,753 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:30:07,756 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:30:09,118 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:30:17,121 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:30:20,459 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:30:28,462 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:30:30,809 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:30:38,814 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:30:42,893 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:30:50,897 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:30:59,789 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:31:07,793 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:31:21,387 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:31:31,130 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:31:32,466 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:31:40,470 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:31:41,822 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:31:49,826 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:31:51,323 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:31:59,326 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:32:02,725 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:32:10,728 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:32:13,199 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:32:21,202 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:32:25,183 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:32:33,187 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:32:42,128 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:32:50,132 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:33:03,704 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:33:13,470 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:33:14,860 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:33:22,864 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:33:24,281 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:33:32,284 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:33:33,646 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:33:41,650 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:33:45,037 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:33:53,041 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:33:55,461 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:34:03,466 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:34:07,480 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:34:15,484 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:34:24,431 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:34:32,435 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:34:46,032 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:34:55,797 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:34:57,283 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:35:05,287 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:35:06,616 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:35:14,620 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:35:16,351 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:35:26,162 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:35:28,986 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:35:36,990 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:35:39,345 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:35:47,349 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:35:49,348 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:35:57,352 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:36:00,947 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:36:08,952 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:36:11,527 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:36:19,531 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:36:23,580 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:36:31,584 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:36:40,557 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:36:48,561 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:37:02,167 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:37:11,922 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:37:14,024 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:37:22,028 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:37:23,800 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:37:31,804 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:37:33,805 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:37:43,461 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:37:45,546 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:37:53,550 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:37:55,660 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:38:03,664 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:38:06,094 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:38:14,098 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:38:18,182 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:38:26,187 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:38:27,886 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:38:35,889 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:38:37,807 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:38:45,810 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:38:47,368 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:38:57,202 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:38:58,608 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:39:06,612 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:39:07,903 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:39:15,907 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:39:17,310 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:39:27,175 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:39:28,538 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:39:36,542 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:39:37,941 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:39:45,945 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:39:47,370 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:39:55,374 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:39:58,863 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:40:06,866 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:40:09,751 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:40:17,755 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:40:21,709 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:40:29,713 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:40:38,733 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:40:46,737 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:41:01,011 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:41:10,834 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:41:12,861 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:41:20,865 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:41:22,886 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:41:30,890 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:41:32,976 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:41:42,789 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:41:44,428 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:41:52,433 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:41:54,253 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:42:02,257 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:42:03,880 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:42:11,884 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:42:15,344 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:42:23,347 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:42:25,748 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:42:33,752 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:42:37,995 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:42:45,999 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:42:54,987 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:43:02,991 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:43:16,591 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:43:26,376 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:43:27,978 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:43:35,982 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:43:37,993 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:43:45,997 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:43:47,555 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:43:57,320 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:43:58,759 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:44:06,764 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:44:08,347 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:44:16,350 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:44:18,020 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:44:26,024 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:44:27,745 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:44:37,442 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:44:39,135 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:44:47,138 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:44:49,215 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:44:57,219 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:44:59,020 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:45:08,831 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:45:10,485 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:45:18,489 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:45:21,324 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:45:29,328 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:45:31,114 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:45:40,559 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:45:43,028 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:45:51,031 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:45:53,470 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:46:01,475 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:46:05,481 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:46:14,959 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:46:22,148 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:46:30,152 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:46:43,944 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:46:53,421 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:46:55,199 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:47:03,203 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:47:04,994 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:47:12,997 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:47:14,739 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:47:24,216 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:47:25,862 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:47:33,866 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:47:36,288 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:47:44,293 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:47:48,320 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:47:57,970 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:47:59,300 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:48:07,304 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:48:08,744 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:48:16,748 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:48:18,123 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:48:27,839 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:48:29,201 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:48:37,205 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:48:38,534 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:48:46,538 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:48:48,025 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:48:56,029 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:48:57,686 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:49:07,392 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:49:08,800 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:49:16,805 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:49:18,270 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:49:26,274 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:49:27,921 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:49:37,617 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:49:39,485 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:49:47,488 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:49:49,282 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:49:57,285 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:49:58,922 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:50:06,926 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:50:10,480 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:50:18,485 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:50:20,885 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:50:28,889 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:50:32,930 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:50:40,935 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:50:49,898 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:50:57,902 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:51:11,549 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:51:21,277 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:51:22,689 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:51:30,693 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:51:32,079 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:51:40,083 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:51:41,391 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:51:49,396 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:51:52,605 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:52:00,609 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:52:02,316 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:52:10,319 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:52:11,720 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:52:19,724 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:52:21,396 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:52:31,148 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:52:32,579 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:52:40,583 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:52:41,995 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:52:49,999 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:52:51,312 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:53:01,068 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:53:02,411 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:53:10,414 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:53:11,832 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:53:19,836 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:53:21,304 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:53:29,308 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:53:31,260 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:53:41,034 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:53:42,673 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:53:50,677 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:53:52,668 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:54:00,672 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:54:02,302 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:54:12,102 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:54:13,843 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:54:21,847 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:54:23,459 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:54:31,462 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:54:33,179 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:54:42,893 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:54:44,618 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:54:52,622 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:54:54,352 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:55:02,357 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:55:04,005 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:55:12,009 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:55:15,930 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:55:23,935 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:55:26,343 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:55:34,347 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:55:38,654 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:55:46,658 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:55:55,500 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:56:03,504 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:56:17,097 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:56:26,765 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:56:28,495 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:56:36,499 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:56:38,412 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:56:46,417 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:56:47,830 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:56:57,501 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:56:58,884 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:57:06,889 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:57:08,262 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:57:16,265 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:57:18,074 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:57:26,079 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:57:27,762 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:57:37,435 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:57:38,964 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:57:46,968 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:57:48,390 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:57:56,393 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:57:57,833 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:58:07,516 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:58:09,000 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:58:17,004 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:58:18,678 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:58:26,683 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:58:28,319 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:58:36,323 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:58:40,209 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:58:48,213 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:58:50,618 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:58:58,622 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:59:02,629 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:59:10,633 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:59:19,571 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:59:27,575 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:59:41,179 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 18:59:50,995 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 18:59:52,568 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:00:00,571 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:00:02,224 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:00:10,227 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:00:11,911 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:00:21,751 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:00:23,691 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:00:31,695 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:00:33,593 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:00:41,597 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:00:43,294 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:00:51,298 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:00:54,984 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:01:02,989 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:01:04,783 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:01:12,787 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:01:14,183 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:01:22,188 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:01:23,697 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:01:33,428 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:01:34,708 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:01:42,712 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:01:44,099 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:01:52,104 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:01:53,585 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:02:03,330 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:02:04,728 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:02:12,732 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:02:14,176 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:02:22,180 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:02:23,435 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:02:31,439 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:02:33,048 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:02:42,806 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:02:44,123 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:02:52,127 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:02:53,535 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:03:01,539 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:03:02,950 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:03:12,704 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:03:13,996 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:03:22,001 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:03:23,298 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:03:31,303 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:03:32,900 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:03:40,904 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:03:42,583 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:03:52,336 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:03:53,642 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:04:01,646 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:04:03,091 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:04:11,095 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:04:12,450 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:04:22,481 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:04:24,163 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:04:32,167 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:04:34,129 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:04:42,133 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:04:43,731 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:04:53,453 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:04:55,022 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:05:03,025 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:05:04,818 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:05:12,822 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:05:14,456 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:05:22,460 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:05:24,223 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:05:33,966 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:05:35,542 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:05:43,545 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:05:45,016 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:05:53,020 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:05:54,386 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:06:04,128 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:06:05,496 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:06:13,501 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:06:14,863 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:06:22,867 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:06:24,255 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:06:32,259 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:06:35,618 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:06:43,622 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:06:46,235 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:06:54,239 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:06:58,249 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:07:06,252 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:07:15,147 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:07:23,151 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:07:36,703 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:07:46,449 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:07:47,786 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:07:55,791 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:07:57,174 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:08:05,179 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:08:06,441 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:08:14,446 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:08:17,761 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:08:25,765 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:08:28,148 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:08:36,152 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:08:40,120 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:08:48,124 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:08:57,059 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:09:05,063 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:09:18,647 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:09:28,394 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:09:29,715 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:09:37,719 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:09:39,000 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:09:47,004 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:09:48,329 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:09:56,334 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:09:59,622 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:10:07,625 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:10:10,089 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:10:18,092 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:10:22,092 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:10:30,096 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:10:38,960 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:10:46,964 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:11:00,823 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:11:10,743 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:11:12,588 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:11:20,592 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:11:22,200 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:11:30,204 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:11:32,193 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:11:42,175 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:11:43,894 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:11:51,899 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:11:53,631 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:12:01,635 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:12:03,135 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:12:11,138 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:12:12,858 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:12:22,255 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:12:24,648 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:12:32,652 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:12:36,835 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:12:44,838 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:12:53,716 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:13:01,720 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:13:15,277 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:13:25,031 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:13:26,454 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:13:34,458 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:13:35,629 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:13:43,633 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:13:44,870 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:13:52,874 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:13:54,450 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:14:04,223 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:14:05,522 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:14:13,525 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:14:14,857 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:14:22,861 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:14:24,102 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:14:33,891 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:14:35,283 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:14:43,286 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:14:44,539 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:14:52,543 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:14:53,887 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:15:01,890 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:15:05,197 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:15:13,201 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:15:14,490 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:15:22,494 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:15:24,067 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:15:32,071 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:15:33,550 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:15:43,333 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:15:44,932 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:15:52,936 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:15:54,670 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:16:02,674 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:16:04,363 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:16:14,147 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:16:15,951 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:16:23,954 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:16:25,775 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:16:33,778 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:16:35,482 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:16:43,486 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:16:45,268 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:16:55,117 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:16:56,708 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:17:04,712 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:17:06,219 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:17:14,223 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:17:16,215 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:17:26,022 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:17:28,462 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:17:36,466 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:17:38,120 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:17:46,123 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:17:47,593 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:17:57,348 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:17:59,031 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:18:07,035 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:18:08,342 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:18:16,346 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:18:17,749 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:18:25,753 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:18:27,394 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:18:37,185 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:18:38,351 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:18:46,354 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:18:47,671 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:18:55,675 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:18:57,143 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:19:06,985 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:19:08,456 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:19:16,460 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:19:17,752 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:19:25,756 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:19:27,116 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:19:35,120 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:19:38,727 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:19:46,732 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:19:49,148 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:19:57,152 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:20:01,108 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:20:09,112 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
2025-05-31 19:20:18,389 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-31 19:20:26,393 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis-krabulon:6379. Temporary failure in name resolution.
